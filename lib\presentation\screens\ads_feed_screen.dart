import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';

import '../../core/models/ad_model.dart';
import '../../features/ads/bloc/ads_bloc.dart';
import '../widgets/ad_card.dart';
import '../widgets/loading_shimmer.dart';
import '../widgets/error_widget.dart';
import '../widgets/filter_bottom_sheet.dart';

class AdsFeedScreen extends StatefulWidget {
  const AdsFeedScreen({super.key});

  @override
  State<AdsFeedScreen> createState() => _AdsFeedScreenState();
}

class _AdsFeedScreenState extends State<AdsFeedScreen>
    with AutomaticKeepAliveClientMixin {
  late PageController _pageController;
  int _currentIndex = 0;
  Map<int, int> _currentImageIndex = {};
  Map<int, bool> _videoPlayingState = {};

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
    
    // تسجيل مشاهدة الإعلان
    final state = context.read<AdsBloc>().state;
    if (state is AdsLoaded && index < state.filteredAds.length) {
      context.read<AdsBloc>().add(ViewAd(state.filteredAds[index].id));
    }
  }

  void _nextImage(int adIndex, AdModel ad) {
    setState(() {
      int currentImg = _currentImageIndex[adIndex] ?? 0;
      if (currentImg < ad.mediaUrls.length - 1) {
        _currentImageIndex[adIndex] = currentImg + 1;
      }
    });
  }

  void _previousImage(int adIndex, AdModel ad) {
    setState(() {
      int currentImg = _currentImageIndex[adIndex] ?? 0;
      if (currentImg > 0) {
        _currentImageIndex[adIndex] = currentImg - 1;
      }
    });
  }

  void _toggleVideoPlayback(int adIndex) {
    setState(() {
      _videoPlayingState[adIndex] = !(_videoPlayingState[adIndex] ?? false);
    });
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const FilterBottomSheet(),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'StoryAd',
          style: theme.textTheme.headlineSmall?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _showFilterBottomSheet,
            icon: const Icon(Icons.tune, color: Colors.white),
          ),
          IconButton(
            onPressed: () {
              context.read<AdsBloc>().add(const RefreshAds());
            },
            icon: const Icon(Icons.refresh, color: Colors.white),
          ),
        ],
      ),
      body: BlocBuilder<AdsBloc, AdsState>(
        builder: (context, state) {
          if (state is AdsLoading) {
            return const LoadingShimmer();
          }
          
          if (state is AdsError) {
            return CustomErrorWidget(
              message: state.message,
              onRetry: () {
                context.read<AdsBloc>().add(const LoadAds());
              },
            );
          }
          
          if (state is AdsLoaded) {
            if (state.filteredAds.isEmpty) {
              return _buildEmptyState();
            }
            
            return PageView.builder(
              controller: _pageController,
              scrollDirection: Axis.vertical,
              onPageChanged: _onPageChanged,
              itemCount: state.filteredAds.length,
              itemBuilder: (context, index) {
                final ad = state.filteredAds[index];
                return AdCard(
                  ad: ad,
                  adIndex: index,
                  currentImageIndex: _currentImageIndex[index] ?? 0,
                  isVideoPlaying: _videoPlayingState[index] ?? ad.isVideo,
                  isLiked: state.likedAds.contains(ad.id),
                  onNextImage: () => _nextImage(index, ad),
                  onPreviousImage: () => _previousImage(index, ad),
                  onToggleVideo: () => _toggleVideoPlayback(index),
                  onLike: () {
                    if (state.likedAds.contains(ad.id)) {
                      context.read<AdsBloc>().add(UnlikeAd(ad.id));
                    } else {
                      context.read<AdsBloc>().add(LikeAd(ad.id));
                    }
                  },
                  onShare: () {
                    context.read<AdsBloc>().add(ShareAd(ad.id));
                    _showShareDialog(ad);
                  },
                  onComment: () => _showCommentsDialog(ad),
                  onChat: () => _showChatDialog(ad),
                );
              },
            );
          }
          
          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 80,
            color: Colors.white.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No ads found',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your filters',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              context.read<AdsBloc>().add(const FilterAdsByCategory([]));
            },
            child: const Text('Clear Filters'),
          ),
        ],
      ).animate().fadeIn(duration: const Duration(milliseconds: 600)),
    );
  }

  void _showCommentsDialog(AdModel ad) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Text(
                    'Comments',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const Spacer(),
                  Text(
                    '${ad.stats.comments}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            const Divider(height: 1),
            const Expanded(
              child: Center(
                child: Text('Comments feature coming soon!'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showShareDialog(AdModel ad) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Text(
              'Share Ad',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 20),
            const Text('Share feature coming soon!'),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _showChatDialog(AdModel ad) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Text(
              'Chat with ${ad.advertiser.username}',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 20),
            const Text('Chat feature coming soon!'),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
