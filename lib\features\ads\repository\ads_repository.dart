import 'package:hive/hive.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/models/ad_model.dart';
import '../../../core/models/user_model.dart';

class AdsRepository {
  static const String _adsBoxName = 'ads';
  static const String _likedAdsKey = 'liked_ads';
  static const String _viewedAdsKey = 'viewed_ads';
  
  late Box<AdModel> _adsBox;
  late SharedPreferences _prefs;
  
  Future<void> initialize() async {
    _adsBox = await Hive.openBox<AdModel>(_adsBoxName);
    _prefs = await SharedPreferences.getInstance();
    
    // إضافة بيانات تجريبية إذا كانت فارغة
    if (_adsBox.isEmpty) {
      await _addSampleData();
    }
  }
  
  Future<List<AdModel>> getAds() async {
    return _adsBox.values.toList();
  }
  
  Future<AdModel?> getAdById(String id) async {
    return _adsBox.get(id);
  }
  
  Future<void> addAd(AdModel ad) async {
    await _adsBox.put(ad.id, ad);
  }
  
  Future<void> updateAd(AdModel ad) async {
    await _adsBox.put(ad.id, ad);
  }
  
  Future<void> deleteAd(String id) async {
    await _adsBox.delete(id);
  }
  
  Future<Set<String>> getLikedAds() async {
    final likedAds = _prefs.getStringList(_likedAdsKey) ?? [];
    return Set<String>.from(likedAds);
  }
  
  Future<void> likeAd(String adId) async {
    final likedAds = await getLikedAds();
    likedAds.add(adId);
    await _prefs.setStringList(_likedAdsKey, likedAds.toList());
    
    // تحديث إحصائيات الإعلان
    final ad = await getAdById(adId);
    if (ad != null) {
      final updatedStats = ad.stats.copyWith(likes: ad.stats.likes + 1);
      final updatedAd = ad.copyWith(stats: updatedStats);
      await updateAd(updatedAd);
    }
  }
  
  Future<void> unlikeAd(String adId) async {
    final likedAds = await getLikedAds();
    likedAds.remove(adId);
    await _prefs.setStringList(_likedAdsKey, likedAds.toList());
    
    // تحديث إحصائيات الإعلان
    final ad = await getAdById(adId);
    if (ad != null) {
      final updatedStats = ad.stats.copyWith(likes: ad.stats.likes - 1);
      final updatedAd = ad.copyWith(stats: updatedStats);
      await updateAd(updatedAd);
    }
  }
  
  Future<Set<String>> getViewedAds() async {
    final viewedAds = _prefs.getStringList(_viewedAdsKey) ?? [];
    return Set<String>.from(viewedAds);
  }
  
  Future<void> markAdAsViewed(String adId) async {
    final viewedAds = await getViewedAds();
    if (!viewedAds.contains(adId)) {
      viewedAds.add(adId);
      await _prefs.setStringList(_viewedAdsKey, viewedAds.toList());
      await incrementAdViews(adId);
    }
  }
  
  Future<void> incrementAdViews(String adId) async {
    final ad = await getAdById(adId);
    if (ad != null) {
      final updatedStats = ad.stats.copyWith(views: ad.stats.views + 1);
      final updatedAd = ad.copyWith(stats: updatedStats);
      await updateAd(updatedAd);
    }
  }
  
  Future<void> incrementAdShares(String adId) async {
    final ad = await getAdById(adId);
    if (ad != null) {
      final updatedStats = ad.stats.copyWith(shares: ad.stats.shares + 1);
      final updatedAd = ad.copyWith(stats: updatedStats);
      await updateAd(updatedAd);
    }
  }
  
  Future<void> incrementAdComments(String adId) async {
    final ad = await getAdById(adId);
    if (ad != null) {
      final updatedStats = ad.stats.copyWith(comments: ad.stats.comments + 1);
      final updatedAd = ad.copyWith(stats: updatedStats);
      await updateAd(updatedAd);
    }
  }
  
  Future<List<AdModel>> getAdsByCategory(AdCategory category) async {
    final allAds = await getAds();
    return allAds.where((ad) => ad.category == category).toList();
  }
  
  Future<List<AdModel>> searchAds(String query) async {
    final allAds = await getAds();
    final searchQuery = query.toLowerCase();
    
    return allAds.where((ad) {
      return ad.title.toLowerCase().contains(searchQuery) ||
             ad.description.toLowerCase().contains(searchQuery) ||
             ad.tags.any((tag) => tag.toLowerCase().contains(searchQuery));
    }).toList();
  }
  
  Future<void> _addSampleData() async {
    final sampleAdvertiser = UserModel(
      id: 'advertiser_1',
      username: 'TechStore',
      email: '<EMAIL>',
      profileImageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      bio: 'Your trusted tech partner',
      followersCount: 15420,
      isVerified: true,
      createdAt: DateTime.now().subtract(const Duration(days: 365)),
      lastActiveAt: DateTime.now(),
      userType: UserType.advertiser,
    );
    
    final sampleAds = [
      AdModel(
        id: 'ad_1',
        title: 'New Electric Car with Advanced Technology Features',
        description: 'Discover the latest electric car with advanced features, eco-friendly design, and cutting-edge technology that revolutionizes driving experience. Perfect for modern lifestyles!',
        mediaUrls: ['https://images.unsplash.com/photo-1593941707882-a5bac6861d75?w=400&h=800&fit=crop'],
        category: AdCategory.cars,
        advertiser: sampleAdvertiser,
        isVideo: true,
        price: 45000.0,
        currency: 'USD',
        tags: ['electric', 'eco-friendly', 'technology', 'modern'],
        stats: const AdStats(views: 12500, likes: 890, comments: 156, shares: 234),
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        priority: AdPriority.high,
      ),
      
      AdModel(
        id: 'ad_2',
        title: 'Summer Fashion Sale with Huge Discounts',
        description: 'Get 50% off on summer outfits! Explore the latest trends in fashion with stylish dresses, accessories, and more for the perfect summer look. Limited time offer!',
        mediaUrls: [
          'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=800&fit=crop',
          'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=400&h=800&fit=crop',
          'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=800&fit=crop',
        ],
        category: AdCategory.fashion,
        advertiser: sampleAdvertiser.copyWith(
          id: 'advertiser_2',
          username: 'FashionHub',
          email: '<EMAIL>',
        ),
        price: 29.99,
        currency: 'USD',
        tags: ['fashion', 'summer', 'sale', 'discount'],
        stats: const AdStats(views: 8750, likes: 654, comments: 89, shares: 123),
        createdAt: DateTime.now().subtract(const Duration(hours: 5)),
        priority: AdPriority.premium,
      ),
      
      AdModel(
        id: 'ad_3',
        title: 'Latest Smartphone with Amazing Camera',
        description: 'Check out the newest smartphone with a great camera, powerful processor, and sleek design. Ideal for photography enthusiasts and tech lovers alike!',
        mediaUrls: ['https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=800&fit=crop'],
        category: AdCategory.technology,
        advertiser: sampleAdvertiser,
        price: 899.99,
        currency: 'USD',
        tags: ['smartphone', 'camera', 'technology', 'photography'],
        stats: const AdStats(views: 15200, likes: 1205, comments: 298, shares: 456),
        createdAt: DateTime.now().subtract(const Duration(hours: 8)),
        priority: AdPriority.high,
      ),
      
      AdModel(
        id: 'ad_4',
        title: 'Luxury Watch Collection',
        description: 'Discover our premium watch collection with Swiss craftsmanship and elegant designs. Perfect for special occasions and daily wear.',
        mediaUrls: [
          'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=800&fit=crop',
          'https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=400&h=800&fit=crop',
          'https://images.unsplash.com/photo-1533139502658-0198f920d8e8?w=400&h=800&fit=crop',
          'https://images.unsplash.com/photo-1522312346375-d1a52e2b99b3?w=400&h=800&fit=crop',
        ],
        category: AdCategory.fashion,
        advertiser: sampleAdvertiser.copyWith(
          id: 'advertiser_3',
          username: 'LuxuryTime',
          email: '<EMAIL>',
        ),
        price: 1299.99,
        currency: 'USD',
        tags: ['luxury', 'watch', 'swiss', 'elegant'],
        stats: const AdStats(views: 6800, likes: 445, comments: 67, shares: 89),
        createdAt: DateTime.now().subtract(const Duration(hours: 12)),
        priority: AdPriority.normal,
      ),
      
      AdModel(
        id: 'ad_5',
        title: 'Healthy Food Recipes',
        description: 'Learn to cook delicious and healthy meals with our step-by-step recipes. Perfect for maintaining a balanced lifestyle.',
        mediaUrls: [
          'https://images.unsplash.com/photo-**********-ba9599a7e63c?w=400&h=800&fit=crop',
          'https://images.unsplash.com/photo-**********-a2132b4ba21d?w=400&h=800&fit=crop',
          'https://images.unsplash.com/photo-**********-92c53300491e?w=400&h=800&fit=crop',
        ],
        category: AdCategory.food,
        advertiser: sampleAdvertiser.copyWith(
          id: 'advertiser_4',
          username: 'HealthyEats',
          email: '<EMAIL>',
        ),
        tags: ['healthy', 'food', 'recipes', 'cooking'],
        stats: const AdStats(views: 9200, likes: 723, comments: 145, shares: 267),
        createdAt: DateTime.now().subtract(const Duration(hours: 18)),
        priority: AdPriority.normal,
      ),
    ];
    
    for (final ad in sampleAds) {
      await addAd(ad);
    }
  }
}
