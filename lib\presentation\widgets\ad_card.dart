import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';

import '../../core/models/ad_model.dart';
import '../../core/theme/app_theme.dart';

class AdCard extends StatelessWidget {
  final AdModel ad;
  final int adIndex;
  final int currentImageIndex;
  final bool isVideoPlaying;
  final bool isLiked;
  final VoidCallback onNextImage;
  final VoidCallback onPreviousImage;
  final VoidCallback onToggleVideo;
  final VoidCallback onLike;
  final VoidCallback onShare;
  final VoidCallback onComment;
  final VoidCallback onChat;

  const AdCard({
    super.key,
    required this.ad,
    required this.adIndex,
    required this.currentImageIndex,
    required this.isVideoPlaying,
    required this.isLiked,
    required this.onNextImage,
    required this.onPreviousImage,
    required this.onToggleVideo,
    required this.onLike,
    required this.onShare,
    required this.onComment,
    required this.onChat,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      fit: StackFit.expand,
      children: [
        // الخلفية الرئيسية
        _buildBackground(),
        
        // النقاط للصور المتعددة
        if (ad.hasMultipleImages)
          Positioned(
            bottom: 120,
            left: 0,
            right: 0,
            child: _buildImageDotsIndicator(),
          ),
        
        // معلومات الإعلان في الأسفل
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: _buildAdInfo(context),
        ),
        
        // الأيقونات الجانبية
        Positioned(
          right: 12,
          bottom: 120,
          child: _buildActionButtons(context),
        ),
      ],
    );
  }

  Widget _buildBackground() {
    return GestureDetector(
      onTapUp: (details) {
        if (ad.isVideo) {
          onToggleVideo();
        } else if (ad.hasMultipleImages) {
          final screenWidth = details.globalPosition.dx;
          final totalWidth = details.localPosition.dx * 2;
          
          if (screenWidth > totalWidth / 2) {
            onNextImage();
          } else {
            onPreviousImage();
          }
        }
      },
      child: Stack(
        fit: StackFit.expand,
        children: [
          // الصورة/الفيديو
          CachedNetworkImage(
            imageUrl: ad.mediaUrls[currentImageIndex],
            fit: BoxFit.cover,
            placeholder: (context, url) => Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              child: Container(
                color: Colors.grey[300],
              ),
            ),
            errorWidget: (context, url, error) => Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
                ),
              ),
              child: const Center(
                child: Icon(
                  Icons.image_not_supported,
                  color: Colors.white,
                  size: 80,
                ),
              ),
            ),
          ),
          
          // تدرج في الأسفل
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 200,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.7),
                  ],
                ),
              ),
            ),
          ),
          
          // مؤشر الفيديو المتوقف
          if (ad.isVideo && !isVideoPlaying)
            Container(
              color: Colors.black.withOpacity(0.3),
              child: const Center(
                child: Icon(
                  Icons.play_arrow,
                  color: Colors.white,
                  size: 80,
                ),
              ),
            ).animate().fadeIn(duration: const Duration(milliseconds: 300)),
        ],
      ),
    );
  }

  Widget _buildImageDotsIndicator() {
    return Center(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(
          ad.mediaUrls.length,
          (index) => AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            margin: const EdgeInsets.symmetric(horizontal: 3),
            width: index == currentImageIndex ? 24 : 8,
            height: 8,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: index == currentImageIndex
                  ? Colors.white
                  : Colors.white.withOpacity(0.4),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
          ).animate().scale(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              ),
        ),
      ),
    );
  }

  Widget _buildAdInfo(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // معلومات المعلن
          Row(
            children: [
              // صورة المعلن مع زر الإضافة
              Stack(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                    child: ClipOval(
                      child: CachedNetworkImage(
                        imageUrl: ad.advertiser.profileImageUrl ?? '',
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: AppTheme.primaryColor,
                          child: const Icon(
                            Icons.person,
                            color: Colors.white,
                            size: 30,
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: AppTheme.primaryColor,
                          child: const Icon(
                            Icons.person,
                            color: Colors.white,
                            size: 30,
                          ),
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: -2,
                    right: -2,
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor,
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 2),
                      ),
                      child: const Icon(
                        Icons.add,
                        color: Colors.white,
                        size: 12,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(width: 12),
              
              // اسم المعلن
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          ad.advertiser.username,
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (ad.advertiser.isVerified) ...[
                          const SizedBox(width: 4),
                          const Icon(
                            Icons.verified,
                            color: AppTheme.secondaryColor,
                            size: 16,
                          ),
                        ],
                      ],
                    ),
                    Text(
                      _formatFollowers(ad.advertiser.followersCount),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.white.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
              
              // زر المتابعة
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.white),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'Follow',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // عنوان الإعلان
          Text(
            ad.title,
            style: theme.textTheme.titleLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          
          const SizedBox(height: 8),
          
          // وصف الإعلان
          Text(
            ad.description,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.white.withOpacity(0.9),
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          
          // السعر إذا كان متوفراً
          if (ad.price != null) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                '${ad.currency ?? '\$'}${ad.price!.toStringAsFixed(2)}',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
          
          const SizedBox(height: 80), // مساحة للشريط السفلي
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildActionButton(
          icon: isLiked ? Icons.favorite : Icons.favorite_border,
          color: isLiked ? Colors.red : Colors.white,
          count: _formatCount(ad.stats.likes),
          onTap: onLike,
        ),
        const SizedBox(height: 20),
        _buildActionButton(
          icon: Icons.comment_outlined,
          color: Colors.white,
          count: _formatCount(ad.stats.comments),
          onTap: onComment,
        ),
        const SizedBox(height: 20),
        _buildActionButton(
          icon: Icons.share_outlined,
          color: Colors.white,
          count: _formatCount(ad.stats.shares),
          onTap: onShare,
        ),
        const SizedBox(height: 20),
        _buildActionButton(
          icon: Icons.chat_bubble_outline,
          color: Colors.white,
          count: '0',
          onTap: onChat,
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    required String count,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.3),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: color,
              size: 28,
            ),
          ).animate(target: 1).scale(
                duration: const Duration(milliseconds: 100),
                curve: Curves.easeInOut,
              ),
          const SizedBox(height: 4),
          Text(
            count,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  String _formatCount(int count) {
    if (count >= 1000000) {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    }
    return count.toString();
  }

  String _formatFollowers(int count) {
    if (count >= 1000000) {
      return '${(count / 1000000).toStringAsFixed(1)}M followers';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}K followers';
    }
    return '$count followers';
  }
}
