import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

// نموذج بيانات الإعلان
class Ad {
  final String title;
  final String description;
  final String imageUrl;
  final String category;
  final String advertiser;

  Ad({
    required this.title,
    required this.description,
    required this.imageUrl,
    required this.category,
    required this.advertiser,
  });
}

class StoryScreen extends StatefulWidget {
  const StoryScreen({super.key});

  @override
  _StoryScreenState createState() => _StoryScreenState();
}

class _StoryScreenState extends State<StoryScreen> with SingleTickerProviderStateMixin {
  List<Ad> allAds = [
    Ad(
      title: "New Electric Car with Advanced Technology Features",
      description: "Discover the latest electric car with advanced features, eco-friendly design, and cutting-edge technology that revolutionizes driving experience. Perfect for modern lifestyles!",
      imageUrl: "https://via.placeholder.com/300x600.png?text=Car+Ad",
      category: "Cars",
      advertiser: "AutoTech",
    ),
    Ad(
      title: "Summer Fashion Sale with Huge Discounts",
      description: "Get 50% off on summer outfits! Explore the latest trends in fashion with stylish dresses, accessories, and more for the perfect summer look. Limited time offer!",
      imageUrl: "https://via.placeholder.com/300x600.png?text=Fashion+Ad",
      category: "Fashion",
      advertiser: "FashionHub",
    ),
    Ad(
      title: "Latest Smartphone with Amazing Camera",
      description: "Check out the newest smartphone with a great camera, powerful processor, and sleek design. Ideal for photography enthusiasts and tech lovers alike!",
      imageUrl: "https://via.placeholder.com/300x600.png?text=Tech+Ad",
      category: "Technology",
      advertiser: "TechTrend",
    ),
  ];

  List<Ad> filteredAds = [];
  List<String> selectedCategories = ["Cars", "Fashion", "Technology"];
  List<String> likedAds = [];
  List<String> savedAds = [];
  List<Map<String, String>> comments = [];

  int currentIndex = 0;
  bool autoScrollEnabled = true;
  Timer? autoScrollTimer;
  late AnimationController _controller;
  bool isProgressBarVisible = false;

  @override
  void initState() {
    super.initState();
    filteredAds = allAds;
    _loadPreferences();
    _startAutoScroll();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 5),
    )..addStatusListener((status) {
      if (status == AnimationStatus.completed && autoScrollEnabled) {
        _nextAd();
        _controller.reset();
        _controller.forward();
      }
    });
    _controller.forward();
  }

  Future<void> _loadPreferences() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      likedAds = prefs.getStringList('likedAds') ?? [];
      savedAds = prefs.getStringList('savedAds') ?? [];
      selectedCategories = prefs.getStringList('selectedCategories') ?? ["Cars", "Fashion", "Technology"];
      _filterAds();
    });
  }

  void _filterAds() {
    setState(() {
      filteredAds = allAds.where((ad) => selectedCategories.contains(ad.category)).toList();
      currentIndex = 0;
    });
  }

  void _startAutoScroll() {
    if (autoScrollEnabled && filteredAds.isNotEmpty) {
      autoScrollTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
        if (autoScrollEnabled) {
          _nextAd();
          _controller.reset();
          _controller.forward();
        }
      });
    }
  }

  void _stopAutoScroll() {
    autoScrollTimer?.cancel();
  }

  void _nextAd() {
    if (filteredAds.isNotEmpty) {
      setState(() {
        currentIndex = (currentIndex + 1) % filteredAds.length;
      });
    }
  }

  void _toggleAutoScroll() {
    setState(() {
      autoScrollEnabled = !autoScrollEnabled;
      if (autoScrollEnabled) {
        _startAutoScroll();
        _controller.forward();
      } else {
        _stopAutoScroll();
      }
    });
  }

  Future<void> _toggleLike(String adTitle) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      if (likedAds.contains(adTitle)) {
        likedAds.remove(adTitle);
      } else {
        likedAds.add(adTitle);
      }
      prefs.setStringList('likedAds', likedAds);
    });
  }

  void _showCommentsDialog(Ad ad) {
    TextEditingController commentController = TextEditingController();
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('Comments for ${ad.title}', style: const TextStyle(fontFamily: 'Poppins')),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: commentController,
                decoration: InputDecoration(hintText: 'Add a comment...', hintStyle: const TextStyle(fontFamily: 'Poppins')),
              ),
              const SizedBox(height: 10),
              ListView.builder(
                shrinkWrap: true,
                itemCount: comments.where((c) => c['adTitle'] == ad.title).length,
                itemBuilder: (context, index) {
                  var comment = comments.where((c) => c['adTitle'] == ad.title).toList()[index];
                  return ListTile(
                    title: Text(comment['comment']!, style: const TextStyle(fontFamily: 'Poppins')),
                  );
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                if (commentController.text.isNotEmpty) {
                  _addComment(ad.title, commentController.text);
                  commentController.clear();
                }
                Navigator.pop(context);
              },
              child: const Text('Add', style: TextStyle(fontFamily: 'Poppins')),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close', style: TextStyle(fontFamily: 'Poppins')),
            ),
          ],
        );
      },
    );
  }

  void _addComment(String adTitle, String comment) {
    setState(() {
      comments.add({'adTitle': adTitle, 'comment': comment});
    });
  }

  void _showShareDialog(Ad ad) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('Share ${ad.title}', style: const TextStyle(fontFamily: 'Poppins')),
          content: const Text('Share feature coming soon!', style: TextStyle(fontFamily: 'Poppins')),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close', style: TextStyle(fontFamily: 'Poppins')),
            ),
          ],
        );
      },
    );
  }

  void _showChatDialog(Ad ad) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatScreen(advertiser: ad.advertiser),
      ),
    );
  }

  void _showCategoryFilter() {
    showDialog(
      context: context,
      builder: (context) {
        List<String> tempCategories = List.from(selectedCategories);
        return AlertDialog(
          title: Text(AppLocalizations.of(context)!.selectCategories, style: const TextStyle(fontFamily: 'Poppins')),
          content: SingleChildScrollView(
            child: Column(
              children: [
                CheckboxListTile(
                  title: const Text("Cars", style: TextStyle(fontFamily: 'Poppins')),
                  value: tempCategories.contains("Cars"),
                  onChanged: (value) {
                    setState(() {
                      if (value == true) tempCategories.add("Cars");
                      else tempCategories.remove("Cars");
                    });
                  },
                ),
                CheckboxListTile(
                  title: const Text("Fashion", style: TextStyle(fontFamily: 'Poppins')),
                  value: tempCategories.contains("Fashion"),
                  onChanged: (value) {
                    setState(() {
                      if (value == true) tempCategories.add("Fashion");
                      else tempCategories.remove("Fashion");
                    });
                  },
                ),
                CheckboxListTile(
                  title: const Text("Technology", style: TextStyle(fontFamily: 'Poppins')),
                  value: tempCategories.contains("Technology"),
                  onChanged: (value) {
                    setState(() {
                      if (value == true) tempCategories.add("Technology");
                      else tempCategories.remove("Technology");
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(AppLocalizations.of(context)!.cancel, style: const TextStyle(fontFamily: 'Poppins')),
            ),
            TextButton(
              onPressed: () async {
                SharedPreferences prefs = await SharedPreferences.getInstance();
                setState(() {
                  selectedCategories = tempCategories;
                  prefs.setStringList('selectedCategories', selectedCategories);
                  _filterAds();
                });
                Navigator.pop(context);
              },
              child: Text(AppLocalizations.of(context)!.apply, style: const TextStyle(fontFamily: 'Poppins')),
            ),
          ],
        );
      },
    );
  }

  void _showFullDetails(Ad ad) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(ad.title, style: const TextStyle(fontFamily: 'Poppins')),
          content: Text(ad.description, style: const TextStyle(fontFamily: 'Poppins')),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close', style: TextStyle(fontFamily: 'Poppins')),
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    autoScrollTimer?.cancel();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    if (filteredAds.isEmpty) {
      return Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFF1E90FF), Color(0xFF00CED1)],
            ),
          ),
          child: Center(
            child: Text(
              localizations.noAdsAvailable,
              style: const TextStyle(
                fontSize: 20,
                color: Colors.white,
                fontFamily: 'Poppins',
              ),
            ),
          ),
        ),
      );
    }

    final ad = filteredAds[currentIndex];
    return GestureDetector(
      onTap: () {
        setState(() {
          isProgressBarVisible = !isProgressBarVisible;
        });
      },
      child: Scaffold(
        body: Stack(
          fit: StackFit.expand,
          children: [
            // خلفية مفتوحة بالكامل
            Image.network(
              ad.imageUrl,
              fit: BoxFit.cover,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(color: Colors.grey[300]);
              },
              errorBuilder: (context, error, stackTrace) => Container(color: Colors.grey[300]),
            ),
            SafeArea(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // صورة الملف الشخصي مع + أسفلها بلون أحمر
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Stack(
                          alignment: Alignment.center,
                          children: [
                            Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.black.withOpacity(0.2),
                              ),
                            ),
                            CircleAvatar(
                              radius: 18,
                              backgroundImage: NetworkImage('https://via.placeholder.com/150'),
                              backgroundColor: Colors.transparent,
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        IconButton(
                          icon: const Icon(Icons.add, color: Colors.red, size: 20),
                          onPressed: () {
                            // لاحقًا يمكن إضافة ميزة المتابعة
                          },
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  // أيقونات عمودية على الجانب الأيمن بتصميم عصري
                  Positioned(
                    right: 10,
                    top: 100, // قابل للتعديل حسب الحاجة
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildIconWithCount(
                          icon: likedAds.contains(ad.title) ? Icons.favorite : Icons.favorite_border,
                          color: likedAds.contains(ad.title) ? Colors.red : Colors.white,
                          onPressed: () => _toggleLike(ad.title),
                          count: '9K',
                        ),
                        _buildIconWithCount(
                          icon: Icons.comment,
                          color: Colors.white,
                          onPressed: () => _showCommentsDialog(ad),
                          count: '150',
                        ),
                        _buildIconWithCount(
                          icon: Icons.share,
                          color: Colors.white,
                          onPressed: () => _showShareDialog(ad),
                          count: '13K',
                        ),
                        _buildIconWithCount(
                          icon: Icons.chat,
                          color: Colors.white,
                          onPressed: () => _showChatDialog(ad),
                          count: '0',
                        ),
                      ],
                    ),
                  ),
                  // العنوان والتفاصيل في الأسفل
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          ad.title.length > 50 ? '${ad.title.substring(0, 50)}...' : ad.title,
                          style: const TextStyle(
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontFamily: 'Poppins',
                          ),
                        ),
                        const SizedBox(height: 8),
                        GestureDetector(
                          onTap: () => _showFullDetails(ad),
                          child: Text(
                            ad.description,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.white70,
                              fontFamily: 'Poppins',
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // شريط التقدم في الأسفل
                  if (isProgressBarVisible)
                    Container(
                      height: 3,
                      child: AnimatedBuilder(
                        animation: _controller,
                        builder: (context, child) {
                          return LinearProgressIndicator(
                            value: _controller.value,
                            backgroundColor: Colors.white12,
                            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                          );
                        },
                      ),
                    ),
                ],
              ),
            ),
            // زر التمرير التلقائي
            Positioned(
              right: 16,
              bottom: 16,
              child: IconButton(
                icon: Icon(
                  autoScrollEnabled ? Icons.pause : Icons.play_arrow,
                  color: Colors.white,
                  size: 30,
                ),
                onPressed: _toggleAutoScroll,
              ),
            ),
            // زر التصفية
            Positioned(
              top: 16,
              right: 16,
              child: IconButton(
                icon: const Icon(Icons.filter_list, color: Colors.white, size: 30),
                onPressed: _showCategoryFilter,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIconWithCount({required IconData icon, required Color color, required VoidCallback onPressed, required String count}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.black.withOpacity(0.2),
              boxShadow: [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: IconButton(
              icon: Icon(icon, color: color, size: 28),
              onPressed: onPressed,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            count,
            style: const TextStyle(color: Colors.white, fontFamily: 'Poppins', fontSize: 12),
          ),
        ],
      ),
    );
  }
}

// صفحة الدردشة (بسيطة حاليًا)
class ChatScreen extends StatelessWidget {
  final String advertiser;

  const ChatScreen({super.key, required this.advertiser});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Chat with ${advertiser}', style: const TextStyle(fontFamily: 'Poppins')),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Chatting with ${advertiser}',
              style: const TextStyle(fontSize: 20, fontFamily: 'Poppins'),
            ),
            const SizedBox(height: 20),
            Text(
              'This is a placeholder for the chat feature.',
              style: const TextStyle(fontSize: 16, fontFamily: 'Poppins'),
            ),
          ],
        ),
      ),
    );
  }
}