import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

// نموذج بيانات الإعلان
class Ad {
  final String title;
  final String description;
  final String imageUrl;
  final String category;
  final String advertiser;

  Ad({
    required this.title,
    required this.description,
    required this.imageUrl,
    required this.category,
    required this.advertiser,
  });
}

class StoryScreen extends StatefulWidget {
  const StoryScreen({super.key});

  @override
  _StoryScreenState createState() => _StoryScreenState();
}

class _StoryScreenState extends State<StoryScreen> with SingleTickerProviderStateMixin {
  List<Ad> allAds = [
    Ad(
      title: "New Electric Car with Advanced Technology Features",
      description: "Discover the latest electric car with advanced features, eco-friendly design, and cutting-edge technology that revolutionizes driving experience. Perfect for modern lifestyles!",
      imageUrl: "https://images.unsplash.com/photo-1593941707882-a5bac6861d75?w=400&h=800&fit=crop",
      category: "Cars",
      advertiser: "AutoTech",
    ),
    Ad(
      title: "Summer Fashion Sale with Huge Discounts",
      description: "Get 50% off on summer outfits! Explore the latest trends in fashion with stylish dresses, accessories, and more for the perfect summer look. Limited time offer!",
      imageUrl: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=800&fit=crop",
      category: "Fashion",
      advertiser: "FashionHub",
    ),
    Ad(
      title: "Latest Smartphone with Amazing Camera",
      description: "Check out the newest smartphone with a great camera, powerful processor, and sleek design. Ideal for photography enthusiasts and tech lovers alike!",
      imageUrl: "https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=800&fit=crop",
      category: "Technology",
      advertiser: "TechTrend",
    ),
  ];

  List<Ad> filteredAds = [];
  List<String> selectedCategories = ["Cars", "Fashion", "Technology"];
  List<String> likedAds = [];
  List<String> savedAds = [];
  List<Map<String, String>> comments = [];

  int currentIndex = 0;
  bool autoScrollEnabled = true;
  Timer? autoScrollTimer;
  late AnimationController _controller;
  bool isProgressBarVisible = false;

  @override
  void initState() {
    super.initState();
    filteredAds = allAds;
    _loadPreferences();
    _startAutoScroll();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 5),
    )..addStatusListener((status) {
      if (status == AnimationStatus.completed && autoScrollEnabled) {
        _nextAd();
        _controller.reset();
        _controller.forward();
      }
    });
    _controller.forward();
  }

  Future<void> _loadPreferences() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      likedAds = prefs.getStringList('likedAds') ?? [];
      savedAds = prefs.getStringList('savedAds') ?? [];
      selectedCategories = prefs.getStringList('selectedCategories') ?? ["Cars", "Fashion", "Technology"];
      _filterAds();
    });
  }

  void _filterAds() {
    setState(() {
      filteredAds = allAds.where((ad) => selectedCategories.contains(ad.category)).toList();
      currentIndex = 0;
    });
  }

  void _startAutoScroll() {
    if (autoScrollEnabled && filteredAds.isNotEmpty) {
      autoScrollTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
        if (autoScrollEnabled) {
          _nextAd();
          _controller.reset();
          _controller.forward();
        }
      });
    }
  }

  void _stopAutoScroll() {
    autoScrollTimer?.cancel();
  }

  void _nextAd() {
    if (filteredAds.isNotEmpty) {
      setState(() {
        currentIndex = (currentIndex + 1) % filteredAds.length;
      });
    }
  }

  void _toggleAutoScroll() {
    setState(() {
      autoScrollEnabled = !autoScrollEnabled;
      if (autoScrollEnabled) {
        _startAutoScroll();
        _controller.forward();
      } else {
        _stopAutoScroll();
      }
    });
  }

  Future<void> _toggleLike(String adTitle) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      if (likedAds.contains(adTitle)) {
        likedAds.remove(adTitle);
      } else {
        likedAds.add(adTitle);
      }
      prefs.setStringList('likedAds', likedAds);
    });
  }

  void _showCommentsDialog(Ad ad) {
    TextEditingController commentController = TextEditingController();
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('Comments for ${ad.title}', style: const TextStyle(fontFamily: 'Poppins')),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: commentController,
                decoration: InputDecoration(hintText: 'Add a comment...', hintStyle: const TextStyle(fontFamily: 'Poppins')),
              ),
              const SizedBox(height: 10),
              SizedBox(
                height: 200,
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: comments.where((c) => c['adTitle'] == ad.title).length,
                  itemBuilder: (context, index) {
                    var comment = comments.where((c) => c['adTitle'] == ad.title).toList()[index];
                    return ListTile(
                      title: Text(comment['comment']!, style: const TextStyle(fontFamily: 'Poppins')),
                    );
                  },
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                if (commentController.text.isNotEmpty) {
                  _addComment(ad.title, commentController.text);
                  commentController.clear();
                }
                Navigator.pop(context);
              },
              child: const Text('Add', style: TextStyle(fontFamily: 'Poppins')),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close', style: TextStyle(fontFamily: 'Poppins')),
            ),
          ],
        );
      },
    );
  }

  void _addComment(String adTitle, String comment) {
    setState(() {
      comments.add({'adTitle': adTitle, 'comment': comment});
    });
  }

  void _showShareDialog(Ad ad) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('Share ${ad.title}', style: const TextStyle(fontFamily: 'Poppins')),
          content: const Text('Share feature coming soon!', style: TextStyle(fontFamily: 'Poppins')),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close', style: TextStyle(fontFamily: 'Poppins')),
            ),
          ],
        );
      },
    );
  }

  void _showChatDialog(Ad ad) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatScreen(advertiser: ad.advertiser),
      ),
    );
  }

  void _showCategoryFilter() {
    showDialog(
      context: context,
      builder: (context) {
        List<String> tempCategories = List.from(selectedCategories);
        return AlertDialog(
          title: Text(AppLocalizations.of(context)!.selectCategories, style: const TextStyle(fontFamily: 'Poppins')),
          content: SingleChildScrollView(
            child: Column(
              children: [
                CheckboxListTile(
                  title: const Text("Cars", style: TextStyle(fontFamily: 'Poppins')),
                  value: tempCategories.contains("Cars"),
                  onChanged: (value) {
                    setState(() {
                      if (value == true) tempCategories.add("Cars");
                      else tempCategories.remove("Cars");
                    });
                  },
                ),
                CheckboxListTile(
                  title: const Text("Fashion", style: TextStyle(fontFamily: 'Poppins')),
                  value: tempCategories.contains("Fashion"),
                  onChanged: (value) {
                    setState(() {
                      if (value == true) tempCategories.add("Fashion");
                      else tempCategories.remove("Fashion");
                    });
                  },
                ),
                CheckboxListTile(
                  title: const Text("Technology", style: TextStyle(fontFamily: 'Poppins')),
                  value: tempCategories.contains("Technology"),
                  onChanged: (value) {
                    setState(() {
                      if (value == true) tempCategories.add("Technology");
                      else tempCategories.remove("Technology");
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(AppLocalizations.of(context)!.cancel, style: const TextStyle(fontFamily: 'Poppins')),
            ),
            TextButton(
              onPressed: () async {
                SharedPreferences prefs = await SharedPreferences.getInstance();
                setState(() {
                  selectedCategories = tempCategories;
                  prefs.setStringList('selectedCategories', selectedCategories);
                  _filterAds();
                });
                Navigator.pop(context);
              },
              child: Text(AppLocalizations.of(context)!.apply, style: const TextStyle(fontFamily: 'Poppins')),
            ),
          ],
        );
      },
    );
  }

  void _showFullDetails(Ad ad) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(ad.title, style: const TextStyle(fontFamily: 'Poppins')),
          content: Text(ad.description, style: const TextStyle(fontFamily: 'Poppins')),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close', style: TextStyle(fontFamily: 'Poppins')),
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    autoScrollTimer?.cancel();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    if (filteredAds.isEmpty) {
      return Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFF1E90FF), Color(0xFF00CED1)],
            ),
          ),
          child: Center(
            child: Text(
              localizations.noAdsAvailable,
              style: const TextStyle(
                fontSize: 20,
                color: Colors.white,
                fontFamily: 'Poppins',
              ),
            ),
          ),
        ),
      );
    }

    final ad = filteredAds[currentIndex];
    return GestureDetector(
      onTap: () {
        setState(() {
          isProgressBarVisible = !isProgressBarVisible;
        });
      },
      child: Scaffold(
        body: Stack(
          fit: StackFit.expand,
          children: [
            // خلفية مفتوحة بالكامل
            Image.network(
              ad.imageUrl,
              fit: BoxFit.cover,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [Colors.blue[400]!, Colors.purple[400]!],
                    ),
                  ),
                  child: const Center(
                    child: CircularProgressIndicator(color: Colors.white),
                  ),
                );
              },
              errorBuilder: (context, error, stackTrace) => Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [Colors.blue[400]!, Colors.purple[400]!],
                  ),
                ),
                child: const Center(
                  child: Icon(
                    Icons.image_not_supported,
                    color: Colors.white,
                    size: 80,
                  ),
                ),
              ),
            ),
            SafeArea(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // صورة الملف الشخصي المحسنة
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        Column(
                          children: [
                            Container(
                              width: 50,
                              height: 50,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: LinearGradient(
                                  colors: [Colors.purple, Colors.pink],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.3),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              padding: const EdgeInsets.all(2),
                              child: CircleAvatar(
                                radius: 22,
                                backgroundImage: NetworkImage('https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'),
                                backgroundColor: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Container(
                              width: 24,
                              height: 24,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.red,
                                border: Border.all(color: Colors.white, width: 2),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.3),
                                    blurRadius: 4,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                              ),
                              child: const Icon(
                                Icons.add,
                                color: Colors.white,
                                size: 16,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                ad.advertiser,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: 'Poppins',
                                ),
                              ),
                              Text(
                                ad.category,
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 0.8),
                                  fontSize: 12,
                                  fontFamily: 'Poppins',
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  // العنوان والتفاصيل المحسنة في الأسفل
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withValues(alpha: 0.7),
                        ],
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(16.0, 40.0, 80.0, 16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            ad.title.length > 45 ? '${ad.title.substring(0, 45)}...' : ad.title,
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              fontFamily: 'Poppins',
                              height: 1.2,
                            ),
                          ),
                          const SizedBox(height: 12),
                          GestureDetector(
                            onTap: () => _showFullDetails(ad),
                            child: Text(
                              ad.description.length > 120 ? '${ad.description.substring(0, 120)}... اقرأ المزيد' : ad.description,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white.withValues(alpha: 0.9),
                                fontFamily: 'Poppins',
                                height: 1.4,
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              color: Colors.white.withValues(alpha: 0.2),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: Text(
                              '#${ad.category}',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.white,
                                fontFamily: 'Poppins',
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // شريط التقدم في الأسفل
                  if (isProgressBarVisible)
                    SizedBox(
                      height: 3,
                      child: AnimatedBuilder(
                        animation: _controller,
                        builder: (context, child) {
                          return LinearProgressIndicator(
                            value: _controller.value,
                            backgroundColor: Colors.white.withValues(alpha: 0.2),
                            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                          );
                        },
                      ),
                    ),
                ],
              ),
            ),
            // أيقونات عمودية على الجانب الأيمن بتصميم محسن
            Positioned(
              right: 12,
              top: MediaQuery.of(context).size.height * 0.35,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildModernIconWithCount(
                    icon: likedAds.contains(ad.title) ? Icons.favorite : Icons.favorite_border,
                    color: likedAds.contains(ad.title) ? Colors.red : Colors.white,
                    onPressed: () => _toggleLike(ad.title),
                    count: '9K',
                  ),
                  const SizedBox(height: 20),
                  _buildModernIconWithCount(
                    icon: Icons.comment_outlined,
                    color: Colors.white,
                    onPressed: () => _showCommentsDialog(ad),
                    count: '150',
                  ),
                  const SizedBox(height: 20),
                  _buildModernIconWithCount(
                    icon: Icons.share_outlined,
                    color: Colors.white,
                    onPressed: () => _showShareDialog(ad),
                    count: '13K',
                  ),
                  const SizedBox(height: 20),
                  _buildModernIconWithCount(
                    icon: Icons.chat_bubble_outline,
                    color: Colors.white,
                    onPressed: () => _showChatDialog(ad),
                    count: '0',
                  ),
                ],
              ),
            ),
            // زر التمرير التلقائي
            Positioned(
              right: 16,
              bottom: 80,
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.black.withValues(alpha: 0.3),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: Icon(
                    autoScrollEnabled ? Icons.pause : Icons.play_arrow,
                    color: Colors.white,
                    size: 28,
                  ),
                  onPressed: _toggleAutoScroll,
                ),
              ),
            ),
            // زر التصفية المحسن
            Positioned(
              top: 50,
              right: 16,
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.black.withValues(alpha: 0.4),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  icon: const Icon(Icons.tune, color: Colors.white, size: 24),
                  onPressed: _showCategoryFilter,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernIconWithCount({required IconData icon, required Color color, required VoidCallback onPressed, required String count}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.black.withValues(alpha: 0.4),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.3),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(25),
              onTap: onPressed,
              child: Icon(
                icon,
                color: color,
                size: 26,
              ),
            ),
          ),
        ),
        const SizedBox(height: 6),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.black.withValues(alpha: 0.5),
          ),
          child: Text(
            count,
            style: const TextStyle(
              color: Colors.white,
              fontFamily: 'Poppins',
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}

// صفحة الدردشة (بسيطة حاليًا)
class ChatScreen extends StatelessWidget {
  final String advertiser;

  const ChatScreen({super.key, required this.advertiser});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Chat with ${advertiser}', style: const TextStyle(fontFamily: 'Poppins')),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Chatting with ${advertiser}',
              style: const TextStyle(fontSize: 20, fontFamily: 'Poppins'),
            ),
            const SizedBox(height: 20),
            Text(
              'This is a placeholder for the chat feature.',
              style: const TextStyle(fontSize: 16, fontFamily: 'Poppins'),
            ),
          ],
        ),
      ),
    );
  }
}