import 'package:flutter/material.dart';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class Ad {
  final String title;
  final String description;
  final List<String> imageUrls; // تغيير من imageUrl إلى قائمة من الصور
  final String category;
  final String advertiser;
  final bool isVideo; // إضافة خاصية لتحديد إذا كان فيديو

  Ad({
    required this.title,
    required this.description,
    required this.imageUrls,
    required this.category,
    required this.advertiser,
    this.isVideo = false,
  });

  // للحصول على الصورة الأولى (للتوافق مع الكود القديم)
  String get imageUrl => imageUrls.isNotEmpty ? imageUrls[0] : '';

  // للتحقق من وجود صور متعددة
  bool get hasMultipleImages => !isVideo && imageUrls.length > 1;
}

class StoryScreen extends StatefulWidget {
  const StoryScreen({super.key});

  @override
  _StoryScreenState createState() => _StoryScreenState();
}

class _StoryScreenState extends State<StoryScreen> with SingleTickerProviderStateMixin {
  List<Ad> allAds = [
    // إعلان سيارة كهربائية - فيديو واحد
    Ad(
      title: "New Electric Car with Advanced Technology Features",
      description: "Discover the latest electric car with advanced features, eco-friendly design, and cutting-edge technology that revolutionizes driving experience. Perfect for modern lifestyles!",
      imageUrls: ["https://images.unsplash.com/photo-1593941707882-a5bac6861d75?w=400&h=800&fit=crop"],
      category: "Cars",
      advertiser: "AutoTech",
      isVideo: true, // فيديو - لن تظهر النقاط
    ),
    // إعلان أزياء - صور متعددة
    Ad(
      title: "Summer Fashion Sale with Huge Discounts",
      description: "Get 50% off on summer outfits! Explore the latest trends in fashion with stylish dresses, accessories, and more for the perfect summer look. Limited time offer!",
      imageUrls: [
        "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=800&fit=crop",
        "https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=400&h=800&fit=crop",
        "https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=800&fit=crop",
      ],
      category: "Fashion",
      advertiser: "FashionHub",
      isVideo: false, // صور متعددة - ستظهر النقاط
    ),
    // إعلان تكنولوجيا - صورة واحدة
    Ad(
      title: "Latest Smartphone with Amazing Camera",
      description: "Check out the newest smartphone with a great camera, powerful processor, and sleek design. Ideal for photography enthusiasts and tech lovers alike!",
      imageUrls: ["https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=800&fit=crop"],
      category: "Technology",
      advertiser: "TechTrend",
      isVideo: false, // صورة واحدة - لن تظهر النقاط
    ),
    // إعلان ساعات - صور متعددة
    Ad(
      title: "Luxury Watch Collection",
      description: "Discover our premium watch collection with Swiss craftsmanship and elegant designs. Perfect for special occasions and daily wear.",
      imageUrls: [
        "https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=800&fit=crop",
        "https://images.unsplash.com/photo-1524592094714-0f0654e20314?w=400&h=800&fit=crop",
        "https://images.unsplash.com/photo-1533139502658-0198f920d8e8?w=400&h=800&fit=crop",
        "https://images.unsplash.com/photo-1522312346375-d1a52e2b99b3?w=400&h=800&fit=crop",
      ],
      category: "Fashion",
      advertiser: "LuxuryTime",
      isVideo: false, // صور متعددة - ستظهر النقاط
    ),
    // إعلان طعام - صور متعددة
    Ad(
      title: "Healthy Food Recipes",
      description: "Learn to cook delicious and healthy meals with our step-by-step recipes. Perfect for maintaining a balanced lifestyle.",
      imageUrls: [
        "https://images.unsplash.com/photo-**********-ba9599a7e63c?w=400&h=800&fit=crop",
        "https://images.unsplash.com/photo-**********-a2132b4ba21d?w=400&h=800&fit=crop",
        "https://images.unsplash.com/photo-**********-92c53300491e?w=400&h=800&fit=crop",
      ],
      category: "Food",
      advertiser: "HealthyEats",
      isVideo: false, // صور متعددة - ستظهر النقاط
    ),
  ];

  List<Ad> filteredAds = [];
  List<String> selectedCategories = ["Cars", "Fashion", "Technology", "Food"];
  List<String> likedAds = [];
  List<String> savedAds = [];
  List<Map<String, String>> comments = [];

  int currentIndex = 0;
  Map<int, int> currentImageIndex = {}; // لتتبع الصورة الحالية لكل إعلان
  late PageController _pageController;
  late AnimationController _dotsController;
  late Animation<double> _dotsAnimation;

  @override
  void initState() {
    super.initState();
    filteredAds = allAds;
    _loadPreferences();
    
    _pageController = PageController();
    _dotsController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _dotsAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _dotsController,
      curve: Curves.easeInOut,
    ));
    
    _dotsController.forward();
  }

  Future<void> _loadPreferences() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      likedAds = prefs.getStringList('likedAds') ?? [];
      savedAds = prefs.getStringList('savedAds') ?? [];
      selectedCategories = prefs.getStringList('selectedCategories') ?? ["Cars", "Fashion", "Technology", "Food"];
    });
    _filterAds();
  }

  void _filterAds() {
    setState(() {
      filteredAds = allAds.where((ad) => selectedCategories.contains(ad.category)).toList();
      if (currentIndex >= filteredAds.length) {
        currentIndex = 0;
      }
    });
  }



  void _onPageChanged(int index) {
    setState(() {
      currentIndex = index;
    });
  }

  void _nextImage(int adIndex, Ad ad) {
    setState(() {
      int currentImg = currentImageIndex[adIndex] ?? 0;
      if (currentImg < ad.imageUrls.length - 1) {
        currentImageIndex[adIndex] = currentImg + 1;
      }
    });
  }

  void _previousImage(int adIndex, Ad ad) {
    setState(() {
      int currentImg = currentImageIndex[adIndex] ?? 0;
      if (currentImg > 0) {
        currentImageIndex[adIndex] = currentImg - 1;
      }
    });
  }

  Future<void> _applyFilters(List<String> tempCategories) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      selectedCategories = tempCategories;
    });
    await prefs.setStringList('selectedCategories', selectedCategories);
    _filterAds();
  }

  void _toggleLike(String adTitle) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      if (likedAds.contains(adTitle)) {
        likedAds.remove(adTitle);
      } else {
        likedAds.add(adTitle);
      }
    });
    await prefs.setStringList('likedAds', likedAds);
  }

  @override
  void dispose() {
    _pageController.dispose();
    _dotsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    if (filteredAds.isEmpty) {
      return Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFF1E90FF), Color(0xFF00CED1)],
            ),
          ),
          child: Center(
            child: Text(
              localizations.noAdsAvailable,
              style: const TextStyle(
                fontSize: 20,
                color: Colors.white,
                fontFamily: 'Poppins',
              ),
            ),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // PageView للتمرير العمودي
          PageView.builder(
            controller: _pageController,
            scrollDirection: Axis.vertical,
            onPageChanged: _onPageChanged,
            itemCount: filteredAds.length,
            itemBuilder: (context, index) {
              return _buildStoryPage(filteredAds[index]);
            },
          ),
          
          // مؤشر النقاط
          Positioned(
            right: 16,
            top: MediaQuery.of(context).size.height * 0.15,
            child: _buildDotsIndicator(),
          ),
          
          // زر التصفية المحسن
          Positioned(
            top: 50,
            right: 16,
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.black.withValues(alpha: 0.4),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: IconButton(
                icon: const Icon(Icons.tune, color: Colors.white, size: 24),
                onPressed: _showCategoryFilter,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStoryPage(Ad ad) {
    // تهيئة فهرس الصورة الحالية إذا لم يكن موجوداً
    int adIndex = filteredAds.indexOf(ad);
    currentImageIndex[adIndex] ??= 0;
    int currentImg = currentImageIndex[adIndex]!;

    return Stack(
      children: [
        // خلفية مفتوحة بالكامل مع دعم الصور المتعددة
        GestureDetector(
          onTapUp: (details) {
            if (ad.hasMultipleImages) {
              // تحديد إذا كان النقر على الجانب الأيمن أم الأيسر
              double screenWidth = MediaQuery.of(context).size.width;
              if (details.globalPosition.dx > screenWidth / 2) {
                // النقر على الجانب الأيمن - الصورة التالية
                _nextImage(adIndex, ad);
              } else {
                // النقر على الجانب الأيسر - الصورة السابقة
                _previousImage(adIndex, ad);
              }
            }
          },
          child: Image.network(
            ad.imageUrls[currentImg],
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [Colors.blue[400]!, Colors.purple[400]!],
                  ),
                ),
                child: const Center(
                  child: CircularProgressIndicator(color: Colors.white),
                ),
              );
            },
            errorBuilder: (context, error, stackTrace) => Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Colors.blue[400]!, Colors.purple[400]!],
                ),
              ),
              child: const Center(
                child: Icon(
                  Icons.image_not_supported,
                  color: Colors.white,
                  size: 80,
                ),
              ),
            ),
          ),
        ),

        // أيقونة تشغيل للفيديوهات
        if (ad.isVideo)
          const Center(
            child: Icon(
              Icons.play_circle_filled,
              color: Colors.white,
              size: 80,
            ),
          ),
        
        SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // صورة الملف الشخصي المحسنة
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Stack(
                      children: [
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              colors: [Colors.purple, Colors.pink],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          padding: const EdgeInsets.all(2),
                          child: CircleAvatar(
                            radius: 22,
                            backgroundImage: NetworkImage('https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'),
                            backgroundColor: Colors.white,
                          ),
                        ),
                        // علامة الإضافة ملاصقة للصورة من الأسفل
                        Positioned(
                          bottom: -2,
                          right: -2,
                          child: Container(
                            width: 20,
                            height: 20,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.red,
                              border: Border.all(color: Colors.white, width: 2),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.3),
                                  blurRadius: 4,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.add,
                              color: Colors.white,
                              size: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            ad.advertiser,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Poppins',
                            ),
                          ),
                          Text(
                            ad.category,
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.8),
                              fontSize: 12,
                              fontFamily: 'Poppins',
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              // العنوان والتفاصيل المحسنة في الأسفل
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.7),
                    ],
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16.0, 40.0, 80.0, 16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        ad.title.length > 45 ? '${ad.title.substring(0, 45)}...' : ad.title,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          fontFamily: 'Poppins',
                          height: 1.2,
                        ),
                      ),
                      const SizedBox(height: 12),
                      GestureDetector(
                        onTap: () => _showFullDetails(ad),
                        child: Text(
                          ad.description.length > 120 ? '${ad.description.substring(0, 120)}... اقرأ المزيد' : ad.description,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white.withValues(alpha: 0.9),
                            fontFamily: 'Poppins',
                            height: 1.4,
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          color: Colors.white.withValues(alpha: 0.2),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          '#${ad.category}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.white,
                            fontFamily: 'Poppins',
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),

        // النقاط في الأسفل للصور المتعددة
        if (ad.hasMultipleImages)
          Positioned(
            bottom: 100,
            left: 0,
            right: 0,
            child: _buildImageDotsIndicator(adIndex, ad),
          ),

        // أيقونات عمودية على الجانب الأيمن بتصميم محسن
        Positioned(
          right: 12,
          top: MediaQuery.of(context).size.height * 0.35,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildModernIconWithCount(
                icon: likedAds.contains(ad.title) ? Icons.favorite : Icons.favorite_border,
                color: likedAds.contains(ad.title) ? Colors.red : Colors.white,
                onPressed: () => _toggleLike(ad.title),
                count: '9K',
              ),
              const SizedBox(height: 20),
              _buildModernIconWithCount(
                icon: Icons.comment_outlined,
                color: Colors.white,
                onPressed: () => _showCommentsDialog(ad),
                count: '150',
              ),
              const SizedBox(height: 20),
              _buildModernIconWithCount(
                icon: Icons.share_outlined,
                color: Colors.white,
                onPressed: () => _showShareDialog(ad),
                count: '13K',
              ),
              const SizedBox(height: 20),
              _buildModernIconWithCount(
                icon: Icons.chat_bubble_outline,
                color: Colors.white,
                onPressed: () => _showChatDialog(ad),
                count: '0',
              ),
            ],
          ),
        ),
      ],
    );
  }

  // مؤشر النقاط للتنقل بين الإعلانات (على الجانب)
  Widget _buildDotsIndicator() {
    return AnimatedBuilder(
      animation: _dotsAnimation,
      builder: (context, child) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(
            filteredAds.length,
            (index) => Container(
              margin: const EdgeInsets.symmetric(vertical: 3),
              width: 6,
              height: index == currentIndex ? 20 : 6,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(3),
                color: index == currentIndex
                  ? Colors.white
                  : Colors.white.withValues(alpha: 0.4),
              ),
            ),
          ),
        );
      },
    );
  }

  // مؤشر النقاط للصور المتعددة (في الأسفل)
  Widget _buildImageDotsIndicator(int adIndex, Ad ad) {
    int currentImg = currentImageIndex[adIndex] ?? 0;

    return Center(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(
          ad.imageUrls.length,
          (index) => AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            margin: const EdgeInsets.symmetric(horizontal: 3),
            width: index == currentImg ? 24 : 8,
            height: 8,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: index == currentImg
                ? Colors.white
                : Colors.white.withValues(alpha: 0.4),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernIconWithCount({required IconData icon, required Color color, required VoidCallback onPressed, required String count}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.black.withValues(alpha: 0.4),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.3),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(25),
              onTap: onPressed,
              child: Icon(
                icon,
                color: color,
                size: 26,
              ),
            ),
          ),
        ),
        const SizedBox(height: 6),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Colors.black.withValues(alpha: 0.5),
          ),
          child: Text(
            count,
            style: const TextStyle(
              color: Colors.white,
              fontFamily: 'Poppins',
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  void _showFullDetails(Ad ad) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(ad.title, style: const TextStyle(fontFamily: 'Poppins')),
        content: Text(ad.description, style: const TextStyle(fontFamily: 'Poppins')),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close', style: TextStyle(fontFamily: 'Poppins')),
          ),
        ],
      ),
    );
  }

  void _showCommentsDialog(Ad ad) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Comments for ${ad.title}', style: const TextStyle(fontFamily: 'Poppins')),
        content: const Text('Comments feature coming soon!', style: TextStyle(fontFamily: 'Poppins')),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close', style: TextStyle(fontFamily: 'Poppins')),
          ),
        ],
      ),
    );
  }

  void _showShareDialog(Ad ad) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Share ${ad.title}', style: const TextStyle(fontFamily: 'Poppins')),
        content: const Text('Share feature coming soon!', style: TextStyle(fontFamily: 'Poppins')),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close', style: TextStyle(fontFamily: 'Poppins')),
          ),
        ],
      ),
    );
  }

  void _showChatDialog(Ad ad) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Chat with ${ad.advertiser}', style: const TextStyle(fontFamily: 'Poppins')),
        content: const Text('Chat feature coming soon!', style: TextStyle(fontFamily: 'Poppins')),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close', style: TextStyle(fontFamily: 'Poppins')),
          ),
        ],
      ),
    );
  }

  void _showCategoryFilter() {
    showDialog(
      context: context,
      builder: (context) {
        List<String> tempCategories = List.from(selectedCategories);
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Filter Categories', style: TextStyle(fontFamily: 'Poppins')),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CheckboxListTile(
                    title: const Text('Cars', style: TextStyle(fontFamily: 'Poppins')),
                    value: tempCategories.contains("Cars"),
                    onChanged: (value) {
                      setState(() {
                        if (value == true) {
                          tempCategories.add("Cars");
                        } else {
                          tempCategories.remove("Cars");
                        }
                      });
                    },
                  ),
                  CheckboxListTile(
                    title: const Text('Fashion', style: TextStyle(fontFamily: 'Poppins')),
                    value: tempCategories.contains("Fashion"),
                    onChanged: (value) {
                      setState(() {
                        if (value == true) {
                          tempCategories.add("Fashion");
                        } else {
                          tempCategories.remove("Fashion");
                        }
                      });
                    },
                  ),
                  CheckboxListTile(
                    title: const Text('Technology', style: TextStyle(fontFamily: 'Poppins')),
                    value: tempCategories.contains("Technology"),
                    onChanged: (value) {
                      setState(() {
                        if (value == true) {
                          tempCategories.add("Technology");
                        } else {
                          tempCategories.remove("Technology");
                        }
                      });
                    },
                  ),
                  CheckboxListTile(
                    title: const Text('Food', style: TextStyle(fontFamily: 'Poppins')),
                    value: tempCategories.contains("Food"),
                    onChanged: (value) {
                      setState(() {
                        if (value == true) {
                          tempCategories.add("Food");
                        } else {
                          tempCategories.remove("Food");
                        }
                      });
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Cancel', style: TextStyle(fontFamily: 'Poppins')),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _applyFilters(tempCategories);
                  },
                  child: const Text('Apply', style: TextStyle(fontFamily: 'Poppins')),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
