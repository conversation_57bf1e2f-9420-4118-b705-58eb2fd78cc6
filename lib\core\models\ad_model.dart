import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'user_model.dart';

part 'ad_model.g.dart';

@HiveType(typeId: 2)
class AdModel extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String title;
  
  @HiveField(2)
  final String description;
  
  @HiveField(3)
  final List<String> mediaUrls;
  
  @HiveField(4)
  final AdCategory category;
  
  @HiveField(5)
  final UserModel advertiser;
  
  @HiveField(6)
  final bool isVideo;
  
  @HiveField(7)
  final double? price;
  
  @HiveField(8)
  final String? currency;
  
  @HiveField(9)
  final String? productUrl;
  
  @HiveField(10)
  final List<String> tags;
  
  @HiveField(11)
  final AdStats stats;
  
  @HiveField(12)
  final DateTime createdAt;
  
  @HiveField(13)
  final DateTime? expiresAt;
  
  @HiveField(14)
  final AdStatus status;
  
  @HiveField(15)
  final String? location;
  
  @HiveField(16)
  final AdPriority priority;

  const AdModel({
    required this.id,
    required this.title,
    required this.description,
    required this.mediaUrls,
    required this.category,
    required this.advertiser,
    this.isVideo = false,
    this.price,
    this.currency,
    this.productUrl,
    this.tags = const [],
    required this.stats,
    required this.createdAt,
    this.expiresAt,
    this.status = AdStatus.active,
    this.location,
    this.priority = AdPriority.normal,
  });

  // للتوافق مع الكود القديم
  String get imageUrl => mediaUrls.isNotEmpty ? mediaUrls.first : '';
  List<String> get imageUrls => mediaUrls;
  bool get hasMultipleImages => !isVideo && mediaUrls.length > 1;

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        mediaUrls,
        category,
        advertiser,
        isVideo,
        price,
        currency,
        productUrl,
        tags,
        stats,
        createdAt,
        expiresAt,
        status,
        location,
        priority,
      ];

  AdModel copyWith({
    String? id,
    String? title,
    String? description,
    List<String>? mediaUrls,
    AdCategory? category,
    UserModel? advertiser,
    bool? isVideo,
    double? price,
    String? currency,
    String? productUrl,
    List<String>? tags,
    AdStats? stats,
    DateTime? createdAt,
    DateTime? expiresAt,
    AdStatus? status,
    String? location,
    AdPriority? priority,
  }) {
    return AdModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      mediaUrls: mediaUrls ?? this.mediaUrls,
      category: category ?? this.category,
      advertiser: advertiser ?? this.advertiser,
      isVideo: isVideo ?? this.isVideo,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      productUrl: productUrl ?? this.productUrl,
      tags: tags ?? this.tags,
      stats: stats ?? this.stats,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      status: status ?? this.status,
      location: location ?? this.location,
      priority: priority ?? this.priority,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'mediaUrls': mediaUrls,
      'category': category.name,
      'advertiser': advertiser.toJson(),
      'isVideo': isVideo,
      'price': price,
      'currency': currency,
      'productUrl': productUrl,
      'tags': tags,
      'stats': stats.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'status': status.name,
      'location': location,
      'priority': priority.name,
    };
  }

  factory AdModel.fromJson(Map<String, dynamic> json) {
    return AdModel(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      mediaUrls: List<String>.from(json['mediaUrls']),
      category: AdCategory.values.firstWhere(
        (cat) => cat.name == json['category'],
        orElse: () => AdCategory.other,
      ),
      advertiser: UserModel.fromJson(json['advertiser']),
      isVideo: json['isVideo'] ?? false,
      price: json['price']?.toDouble(),
      currency: json['currency'],
      productUrl: json['productUrl'],
      tags: List<String>.from(json['tags'] ?? []),
      stats: AdStats.fromJson(json['stats']),
      createdAt: DateTime.parse(json['createdAt']),
      expiresAt: json['expiresAt'] != null ? DateTime.parse(json['expiresAt']) : null,
      status: AdStatus.values.firstWhere(
        (status) => status.name == json['status'],
        orElse: () => AdStatus.active,
      ),
      location: json['location'],
      priority: AdPriority.values.firstWhere(
        (priority) => priority.name == json['priority'],
        orElse: () => AdPriority.normal,
      ),
    );
  }
}

@HiveType(typeId: 3)
class AdStats extends Equatable {
  @HiveField(0)
  final int views;
  
  @HiveField(1)
  final int likes;
  
  @HiveField(2)
  final int comments;
  
  @HiveField(3)
  final int shares;
  
  @HiveField(4)
  final int clicks;
  
  @HiveField(5)
  final double engagementRate;

  const AdStats({
    this.views = 0,
    this.likes = 0,
    this.comments = 0,
    this.shares = 0,
    this.clicks = 0,
    this.engagementRate = 0.0,
  });

  @override
  List<Object> get props => [views, likes, comments, shares, clicks, engagementRate];

  AdStats copyWith({
    int? views,
    int? likes,
    int? comments,
    int? shares,
    int? clicks,
    double? engagementRate,
  }) {
    return AdStats(
      views: views ?? this.views,
      likes: likes ?? this.likes,
      comments: comments ?? this.comments,
      shares: shares ?? this.shares,
      clicks: clicks ?? this.clicks,
      engagementRate: engagementRate ?? this.engagementRate,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'views': views,
      'likes': likes,
      'comments': comments,
      'shares': shares,
      'clicks': clicks,
      'engagementRate': engagementRate,
    };
  }

  factory AdStats.fromJson(Map<String, dynamic> json) {
    return AdStats(
      views: json['views'] ?? 0,
      likes: json['likes'] ?? 0,
      comments: json['comments'] ?? 0,
      shares: json['shares'] ?? 0,
      clicks: json['clicks'] ?? 0,
      engagementRate: json['engagementRate']?.toDouble() ?? 0.0,
    );
  }
}

@HiveType(typeId: 4)
enum AdCategory {
  @HiveField(0)
  cars,
  
  @HiveField(1)
  fashion,
  
  @HiveField(2)
  technology,
  
  @HiveField(3)
  food,
  
  @HiveField(4)
  beauty,
  
  @HiveField(5)
  sports,
  
  @HiveField(6)
  travel,
  
  @HiveField(7)
  home,
  
  @HiveField(8)
  health,
  
  @HiveField(9)
  education,
  
  @HiveField(10)
  entertainment,
  
  @HiveField(11)
  other,
}

@HiveType(typeId: 5)
enum AdStatus {
  @HiveField(0)
  active,
  
  @HiveField(1)
  paused,
  
  @HiveField(2)
  expired,
  
  @HiveField(3)
  rejected,
}

@HiveType(typeId: 6)
enum AdPriority {
  @HiveField(0)
  low,
  
  @HiveField(1)
  normal,
  
  @HiveField(2)
  high,
  
  @HiveField(3)
  premium,
}
