import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ExploreScreen extends StatefulWidget {
  const ExploreScreen({super.key});

  @override
  _ExploreScreenState createState() => _ExploreScreenState();
}

class _ExploreScreenState extends State<ExploreScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  
  final List<String> categories = [
    'All', 'Fashion', 'Beauty', 'Food', 'Wellness', 'Travel'
  ];
  
  final List<ExploreItem> exploreItems = [
    ExploreItem(
      title: 'Apartment Inspo',
      author: 'JACKIE',
      likes: 452,
      imageUrl: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=600&fit=crop',
      category: 'Fashion',
      isVideo: true,
    ),
    ExploreItem(
      title: 'Breakfast ideas that taste good! part 2',
      author: 'liza',
      likes: 905,
      imageUrl: 'https://images.unsplash.com/photo-**********-a2132b4ba21d?w=400&h=600&fit=crop',
      category: 'Food',
      isVideo: false,
    ),
    ExploreItem(
      title: 'another double denim look',
      author: 'bahia <3',
      likes: 367,
      imageUrl: 'https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=400&h=600&fit=crop',
      category: 'Fashion',
      isVideo: false,
    ),
    ExploreItem(
      title: 'Anti-Inflammatory Grocery List (save this list)',
      author: 'Michelle G.',
      likes: 480,
      imageUrl: 'https://images.unsplash.com/photo-1542838132-92c53300491e?w=400&h=600&fit=crop',
      category: 'Wellness',
      isVideo: false,
    ),
    ExploreItem(
      title: 'Summer Makeup Tutorial',
      author: 'Beauty Guru',
      likes: 1200,
      imageUrl: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=600&fit=crop',
      category: 'Beauty',
      isVideo: true,
    ),
    ExploreItem(
      title: 'Travel Essentials Pack',
      author: 'Wanderlust',
      likes: 890,
      imageUrl: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=400&h=600&fit=crop',
      category: 'Travel',
      isVideo: false,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: categories.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  List<ExploreItem> get filteredItems {
    List<ExploreItem> filtered = exploreItems;
    
    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((item) => 
        item.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
        item.author.toLowerCase().contains(_searchQuery.toLowerCase())
      ).toList();
    }
    
    // Filter by category
    String selectedCategory = categories[_tabController.index];
    if (selectedCategory != 'All') {
      filtered = filtered.where((item) => item.category == selectedCategory).toList();
    }
    
    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Header with Following/For You tabs and search
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  // Profile icon
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: const Icon(Icons.person_outline, color: Colors.grey),
                  ),
                  const Spacer(),
                  // Following/For You tabs
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Row(
                      children: [
                        _buildHeaderTab('Following', false),
                        _buildHeaderTab('For You', true),
                      ],
                    ),
                  ),
                  const Spacer(),
                  // Search icon
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.search, color: Colors.grey),
                      onPressed: () => _showSearchDialog(),
                    ),
                  ),
                ],
              ),
            ),
            
            // Category tabs
            Container(
              height: 50,
              child: TabBar(
                controller: _tabController,
                isScrollable: true,
                indicatorColor: Colors.black,
                labelColor: Colors.black,
                unselectedLabelColor: Colors.grey,
                labelStyle: const TextStyle(
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w600,
                ),
                unselectedLabelStyle: const TextStyle(
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.normal,
                ),
                tabs: categories.map((category) => Tab(text: category)).toList(),
                onTap: (index) => setState(() {}),
              ),
            ),
            
            // Grid content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: categories.map((category) => _buildGridView()).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderTab(String text, bool isSelected) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      decoration: BoxDecoration(
        color: isSelected ? Colors.black : Colors.transparent,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: isSelected ? Colors.white : Colors.grey[600],
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildGridView() {
    final items = filteredItems;
    
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.7,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
        ),
        itemCount: items.length,
        itemBuilder: (context, index) {
          return _buildGridItem(items[index]);
        },
      ),
    );
  }

  Widget _buildGridItem(ExploreItem item) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Background image
            Image.network(
              item.imageUrl,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                color: Colors.grey[300],
                child: const Icon(Icons.image_not_supported, color: Colors.grey),
              ),
            ),
            
            // Video play button
            if (item.isVideo)
              const Center(
                child: Icon(
                  Icons.play_circle_filled,
                  color: Colors.white,
                  size: 50,
                ),
              ),
            
            // Bottom gradient and text
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.7),
                    ],
                  ),
                ),
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      item.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontFamily: 'Poppins',
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 8,
                          backgroundColor: Colors.white,
                          child: Text(
                            item.author[0].toUpperCase(),
                            style: const TextStyle(
                              fontSize: 8,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        const SizedBox(width: 6),
                        Expanded(
                          child: Text(
                            item.author,
                            style: const TextStyle(
                              color: Colors.white,
                              fontFamily: 'Poppins',
                              fontSize: 10,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(width: 4),
                        const Icon(Icons.favorite, color: Colors.red, size: 12),
                        const SizedBox(width: 2),
                        Text(
                          '${item.likes}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontFamily: 'Poppins',
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Search', style: TextStyle(fontFamily: 'Poppins')),
          content: TextField(
            decoration: const InputDecoration(
              hintText: 'Search posts...',
              hintStyle: TextStyle(fontFamily: 'Poppins'),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Close', style: TextStyle(fontFamily: 'Poppins')),
            ),
          ],
        );
      },
    );
  }
}

class ExploreItem {
  final String title;
  final String author;
  final int likes;
  final String imageUrl;
  final String category;
  final bool isVideo;

  ExploreItem({
    required this.title,
    required this.author,
    required this.likes,
    required this.imageUrl,
    required this.category,
    this.isVideo = false,
  });
}
