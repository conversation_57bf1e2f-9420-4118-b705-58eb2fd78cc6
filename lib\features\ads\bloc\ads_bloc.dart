import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../core/models/ad_model.dart';
import '../repository/ads_repository.dart';

// Events
abstract class AdsEvent extends Equatable {
  const AdsEvent();

  @override
  List<Object?> get props => [];
}

class LoadAds extends AdsEvent {
  const LoadAds();
}

class RefreshAds extends AdsEvent {
  const RefreshAds();
}

class FilterAdsByCategory extends AdsEvent {
  final List<AdCategory> categories;
  
  const FilterAdsByCategory(this.categories);
  
  @override
  List<Object> get props => [categories];
}

class SearchAds extends AdsEvent {
  final String query;
  
  const SearchAds(this.query);
  
  @override
  List<Object> get props => [query];
}

class LikeAd extends AdsEvent {
  final String adId;
  
  const LikeAd(this.adId);
  
  @override
  List<Object> get props => [adId];
}

class UnlikeAd extends AdsEvent {
  final String adId;
  
  const UnlikeAd(this.adId);
  
  @override
  List<Object> get props => [adId];
}

class ViewAd extends AdsEvent {
  final String adId;
  
  const ViewAd(this.adId);
  
  @override
  List<Object> get props => [adId];
}

class ShareAd extends AdsEvent {
  final String adId;
  
  const ShareAd(this.adId);
  
  @override
  List<Object> get props => [adId];
}

// States
abstract class AdsState extends Equatable {
  const AdsState();

  @override
  List<Object?> get props => [];
}

class AdsInitial extends AdsState {
  const AdsInitial();
}

class AdsLoading extends AdsState {
  const AdsLoading();
}

class AdsLoaded extends AdsState {
  final List<AdModel> ads;
  final List<AdModel> filteredAds;
  final List<AdCategory> selectedCategories;
  final String searchQuery;
  final Set<String> likedAds;
  
  const AdsLoaded({
    required this.ads,
    required this.filteredAds,
    this.selectedCategories = const [],
    this.searchQuery = '',
    this.likedAds = const {},
  });
  
  @override
  List<Object> get props => [
    ads, 
    filteredAds, 
    selectedCategories, 
    searchQuery, 
    likedAds
  ];
  
  AdsLoaded copyWith({
    List<AdModel>? ads,
    List<AdModel>? filteredAds,
    List<AdCategory>? selectedCategories,
    String? searchQuery,
    Set<String>? likedAds,
  }) {
    return AdsLoaded(
      ads: ads ?? this.ads,
      filteredAds: filteredAds ?? this.filteredAds,
      selectedCategories: selectedCategories ?? this.selectedCategories,
      searchQuery: searchQuery ?? this.searchQuery,
      likedAds: likedAds ?? this.likedAds,
    );
  }
}

class AdsError extends AdsState {
  final String message;
  
  const AdsError(this.message);
  
  @override
  List<Object> get props => [message];
}

// Bloc
class AdsBloc extends Bloc<AdsEvent, AdsState> {
  final AdsRepository _adsRepository;
  
  AdsBloc({required AdsRepository adsRepository}) 
      : _adsRepository = adsRepository,
        super(const AdsInitial()) {
    
    on<LoadAds>(_onLoadAds);
    on<RefreshAds>(_onRefreshAds);
    on<FilterAdsByCategory>(_onFilterAdsByCategory);
    on<SearchAds>(_onSearchAds);
    on<LikeAd>(_onLikeAd);
    on<UnlikeAd>(_onUnlikeAd);
    on<ViewAd>(_onViewAd);
    on<ShareAd>(_onShareAd);
  }
  
  Future<void> _onLoadAds(LoadAds event, Emitter<AdsState> emit) async {
    try {
      emit(const AdsLoading());
      
      final ads = await _adsRepository.getAds();
      final likedAds = await _adsRepository.getLikedAds();
      
      emit(AdsLoaded(
        ads: ads,
        filteredAds: ads,
        likedAds: likedAds,
      ));
    } catch (e) {
      emit(AdsError('Failed to load ads: ${e.toString()}'));
    }
  }
  
  Future<void> _onRefreshAds(RefreshAds event, Emitter<AdsState> emit) async {
    try {
      final ads = await _adsRepository.getAds();
      final likedAds = await _adsRepository.getLikedAds();
      
      if (state is AdsLoaded) {
        final currentState = state as AdsLoaded;
        final filteredAds = _filterAds(ads, currentState.selectedCategories, currentState.searchQuery);
        
        emit(currentState.copyWith(
          ads: ads,
          filteredAds: filteredAds,
          likedAds: likedAds,
        ));
      } else {
        emit(AdsLoaded(
          ads: ads,
          filteredAds: ads,
          likedAds: likedAds,
        ));
      }
    } catch (e) {
      emit(AdsError('Failed to refresh ads: ${e.toString()}'));
    }
  }
  
  void _onFilterAdsByCategory(FilterAdsByCategory event, Emitter<AdsState> emit) {
    if (state is AdsLoaded) {
      final currentState = state as AdsLoaded;
      final filteredAds = _filterAds(currentState.ads, event.categories, currentState.searchQuery);
      
      emit(currentState.copyWith(
        filteredAds: filteredAds,
        selectedCategories: event.categories,
      ));
    }
  }
  
  void _onSearchAds(SearchAds event, Emitter<AdsState> emit) {
    if (state is AdsLoaded) {
      final currentState = state as AdsLoaded;
      final filteredAds = _filterAds(currentState.ads, currentState.selectedCategories, event.query);
      
      emit(currentState.copyWith(
        filteredAds: filteredAds,
        searchQuery: event.query,
      ));
    }
  }
  
  Future<void> _onLikeAd(LikeAd event, Emitter<AdsState> emit) async {
    if (state is AdsLoaded) {
      final currentState = state as AdsLoaded;
      final updatedLikedAds = Set<String>.from(currentState.likedAds)..add(event.adId);
      
      await _adsRepository.likeAd(event.adId);
      
      emit(currentState.copyWith(likedAds: updatedLikedAds));
    }
  }
  
  Future<void> _onUnlikeAd(UnlikeAd event, Emitter<AdsState> emit) async {
    if (state is AdsLoaded) {
      final currentState = state as AdsLoaded;
      final updatedLikedAds = Set<String>.from(currentState.likedAds)..remove(event.adId);
      
      await _adsRepository.unlikeAd(event.adId);
      
      emit(currentState.copyWith(likedAds: updatedLikedAds));
    }
  }
  
  Future<void> _onViewAd(ViewAd event, Emitter<AdsState> emit) async {
    await _adsRepository.incrementAdViews(event.adId);
  }
  
  Future<void> _onShareAd(ShareAd event, Emitter<AdsState> emit) async {
    await _adsRepository.incrementAdShares(event.adId);
  }
  
  List<AdModel> _filterAds(List<AdModel> ads, List<AdCategory> categories, String searchQuery) {
    var filtered = ads;
    
    // تصفية حسب الفئة
    if (categories.isNotEmpty) {
      filtered = filtered.where((ad) => categories.contains(ad.category)).toList();
    }
    
    // تصفية حسب البحث
    if (searchQuery.isNotEmpty) {
      final query = searchQuery.toLowerCase();
      filtered = filtered.where((ad) {
        return ad.title.toLowerCase().contains(query) ||
               ad.description.toLowerCase().contains(query) ||
               ad.tags.any((tag) => tag.toLowerCase().contains(query));
      }).toList();
    }
    
    // ترتيب حسب الأولوية والتاريخ
    filtered.sort((a, b) {
      // ترتيب حسب الأولوية أولاً
      final priorityComparison = b.priority.index.compareTo(a.priority.index);
      if (priorityComparison != 0) return priorityComparison;
      
      // ثم حسب التاريخ
      return b.createdAt.compareTo(a.createdAt);
    });
    
    return filtered;
  }
}
