import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/models/payment_model.dart';

class PaymentMethodCard extends StatelessWidget {
  final PaymentMethod paymentMethod;
  final PaymentCard? card;
  final bool isSelected;
  final VoidCallback onTap;

  const PaymentMethodCard({
    super.key,
    required this.paymentMethod,
    this.card,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? theme.primaryColor.withOpacity(0.1) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? theme.primaryColor : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // أيقونة طريقة الدفع
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: _getMethodColor().withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getMethodIcon(),
                color: _getMethodColor(),
                size: 24,
              ),
            ),
            
            const SizedBox(width: 16),
            
            // معلومات طريقة الدفع
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getMethodTitle(),
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isSelected ? theme.primaryColor : null,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _getMethodSubtitle(),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            
            // مؤشر الاختيار
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isSelected ? theme.primaryColor : Colors.transparent,
                border: Border.all(
                  color: isSelected ? theme.primaryColor : Colors.grey[400]!,
                  width: 2,
                ),
              ),
              child: isSelected
                  ? const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 16,
                    )
                  : null,
            ),
          ],
        ),
      ),
    ).animate(target: isSelected ? 1 : 0)
     .scale(begin: const Offset(1, 1), end: const Offset(1.02, 1.02));
  }

  IconData _getMethodIcon() {
    switch (paymentMethod) {
      case PaymentMethod.creditCard:
      case PaymentMethod.debitCard:
        return Icons.credit_card;
      case PaymentMethod.paypal:
        return Icons.account_balance_wallet;
      case PaymentMethod.applePay:
        return Icons.apple;
      case PaymentMethod.googlePay:
        return Icons.google;
      case PaymentMethod.bankTransfer:
        return Icons.account_balance;
      case PaymentMethod.cryptocurrency:
        return Icons.currency_bitcoin;
    }
  }

  Color _getMethodColor() {
    switch (paymentMethod) {
      case PaymentMethod.creditCard:
      case PaymentMethod.debitCard:
        return card?.brand.toLowerCase() == 'visa' 
            ? const Color(0xFF1A1F71)
            : card?.brand.toLowerCase() == 'mastercard'
                ? const Color(0xFFEB001B)
                : Colors.blue;
      case PaymentMethod.paypal:
        return const Color(0xFF0070BA);
      case PaymentMethod.applePay:
        return Colors.black;
      case PaymentMethod.googlePay:
        return const Color(0xFF4285F4);
      case PaymentMethod.bankTransfer:
        return Colors.green;
      case PaymentMethod.cryptocurrency:
        return Colors.orange;
    }
  }

  String _getMethodTitle() {
    switch (paymentMethod) {
      case PaymentMethod.creditCard:
      case PaymentMethod.debitCard:
        return card != null 
            ? '${card!.brand} •••• ${card!.last4Digits}'
            : 'Credit/Debit Card';
      case PaymentMethod.paypal:
        return 'PayPal';
      case PaymentMethod.applePay:
        return 'Apple Pay';
      case PaymentMethod.googlePay:
        return 'Google Pay';
      case PaymentMethod.bankTransfer:
        return 'Bank Transfer';
      case PaymentMethod.cryptocurrency:
        return 'Cryptocurrency';
    }
  }

  String _getMethodSubtitle() {
    switch (paymentMethod) {
      case PaymentMethod.creditCard:
      case PaymentMethod.debitCard:
        return card != null 
            ? 'Expires ${card!.expiryDate}'
            : 'Add a new card';
      case PaymentMethod.paypal:
        return 'Pay with your PayPal account';
      case PaymentMethod.applePay:
        return 'Pay with Touch ID or Face ID';
      case PaymentMethod.googlePay:
        return 'Pay with your Google account';
      case PaymentMethod.bankTransfer:
        return 'Direct bank transfer';
      case PaymentMethod.cryptocurrency:
        return 'Pay with Bitcoin or other crypto';
    }
  }
}
