import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../core/models/notification_model.dart';

class NotificationTile extends StatelessWidget {
  final NotificationModel notification;
  final VoidCallback onTap;
  final VoidCallback onMarkAsRead;
  final VoidCallback onDelete;

  const NotificationTile({
    super.key,
    required this.notification,
    required this.onTap,
    required this.onMarkAsRead,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Dismissible(
      key: Key(notification.id),
      direction: DismissDirection.endToStart,
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        decoration: BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Icon(
          Icons.delete,
          color: Colors.white,
          size: 24,
        ),
      ),
      onDismissed: (direction) => onDelete(),
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: notification.isRead ? Colors.white : theme.primaryColor.withOpacity(0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: notification.isRead ? Colors.grey[200]! : theme.primaryColor.withOpacity(0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ListTile(
          onTap: onTap,
          contentPadding: const EdgeInsets.all(16),
          leading: Stack(
            children: [
              // أيقونة أو صورة الإشعار
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: _getTypeColor().withOpacity(0.1),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: notification.imageUrl != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(24),
                        child: CachedNetworkImage(
                          imageUrl: notification.imageUrl!,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Icon(
                            _getTypeIcon(),
                            color: _getTypeColor(),
                            size: 24,
                          ),
                          errorWidget: (context, url, error) => Icon(
                            _getTypeIcon(),
                            color: _getTypeColor(),
                            size: 24,
                          ),
                        ),
                      )
                    : Icon(
                        _getTypeIcon(),
                        color: _getTypeColor(),
                        size: 24,
                      ),
              ),
              
              // نقطة الإشعار غير المقروء
              if (!notification.isRead)
                Positioned(
                  top: 0,
                  right: 0,
                  child: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                  ),
                ),
            ],
          ),
          title: Text(
            notification.title,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: notification.isRead ? FontWeight.normal : FontWeight.w600,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 4),
              Text(
                notification.body,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 12,
                    color: Colors.grey[500],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatTime(notification.createdAt),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.grey[500],
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: _getTypeColor().withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _getTypeLabel(),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: _getTypeColor(),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          trailing: PopupMenuButton<String>(
            icon: Icon(
              Icons.more_vert,
              color: Colors.grey[400],
              size: 20,
            ),
            onSelected: (value) {
              switch (value) {
                case 'mark_read':
                  if (!notification.isRead) onMarkAsRead();
                  break;
                case 'delete':
                  onDelete();
                  break;
              }
            },
            itemBuilder: (context) => [
              if (!notification.isRead)
                const PopupMenuItem(
                  value: 'mark_read',
                  child: Row(
                    children: [
                      Icon(Icons.mark_email_read, size: 16),
                      SizedBox(width: 8),
                      Text('Mark as read'),
                    ],
                  ),
                ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 16, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Delete', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getTypeIcon() {
    switch (notification.type) {
      case NotificationType.general:
        return Icons.info;
      case NotificationType.newAd:
      case NotificationType.followingNewAd:
        return Icons.new_releases;
      case NotificationType.adLiked:
        return Icons.favorite;
      case NotificationType.adCommented:
        return Icons.comment;
      case NotificationType.adShared:
        return Icons.share;
      case NotificationType.newFollower:
        return Icons.person_add;
      case NotificationType.paymentSuccess:
        return Icons.payment;
      case NotificationType.paymentFailed:
        return Icons.payment;
      case NotificationType.adExpired:
        return Icons.schedule;
      case NotificationType.adApproved:
        return Icons.check_circle;
      case NotificationType.adRejected:
        return Icons.cancel;
      case NotificationType.message:
        return Icons.message;
      case NotificationType.promotion:
        return Icons.local_offer;
      case NotificationType.systemUpdate:
        return Icons.system_update;
    }
  }

  Color _getTypeColor() {
    switch (notification.type) {
      case NotificationType.general:
        return Colors.blue;
      case NotificationType.newAd:
      case NotificationType.followingNewAd:
        return Colors.green;
      case NotificationType.adLiked:
        return Colors.red;
      case NotificationType.adCommented:
        return Colors.orange;
      case NotificationType.adShared:
        return Colors.purple;
      case NotificationType.newFollower:
        return Colors.blue;
      case NotificationType.paymentSuccess:
        return Colors.green;
      case NotificationType.paymentFailed:
        return Colors.red;
      case NotificationType.adExpired:
        return Colors.orange;
      case NotificationType.adApproved:
        return Colors.green;
      case NotificationType.adRejected:
        return Colors.red;
      case NotificationType.message:
        return Colors.blue;
      case NotificationType.promotion:
        return Colors.purple;
      case NotificationType.systemUpdate:
        return Colors.grey;
    }
  }

  String _getTypeLabel() {
    switch (notification.type) {
      case NotificationType.general:
        return 'General';
      case NotificationType.newAd:
        return 'New Ad';
      case NotificationType.adLiked:
        return 'Like';
      case NotificationType.adCommented:
        return 'Comment';
      case NotificationType.adShared:
        return 'Share';
      case NotificationType.newFollower:
        return 'Follower';
      case NotificationType.followingNewAd:
        return 'Following';
      case NotificationType.paymentSuccess:
        return 'Payment';
      case NotificationType.paymentFailed:
        return 'Payment';
      case NotificationType.adExpired:
        return 'Expired';
      case NotificationType.adApproved:
        return 'Approved';
      case NotificationType.adRejected:
        return 'Rejected';
      case NotificationType.message:
        return 'Message';
      case NotificationType.promotion:
        return 'Promo';
      case NotificationType.systemUpdate:
        return 'System';
    }
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);
    
    if (difference.inDays > 7) {
      return '${time.day}/${time.month}/${time.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
