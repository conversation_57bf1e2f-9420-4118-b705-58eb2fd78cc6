import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'user_model.dart';

part 'comment_model.g.dart';

@HiveType(typeId: 7)
class CommentModel extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String adId;
  
  @HiveField(2)
  final UserModel user;
  
  @HiveField(3)
  final String content;
  
  @HiveField(4)
  final DateTime createdAt;
  
  @HiveField(5)
  final int likes;
  
  @HiveField(6)
  final List<CommentModel> replies;
  
  @HiveField(7)
  final String? parentCommentId;
  
  @HiveField(8)
  final bool isEdited;
  
  @HiveField(9)
  final DateTime? editedAt;

  const CommentModel({
    required this.id,
    required this.adId,
    required this.user,
    required this.content,
    required this.createdAt,
    this.likes = 0,
    this.replies = const [],
    this.parentCommentId,
    this.isEdited = false,
    this.editedAt,
  });

  @override
  List<Object?> get props => [
        id,
        adId,
        user,
        content,
        createdAt,
        likes,
        replies,
        parentCommentId,
        isEdited,
        editedAt,
      ];

  CommentModel copyWith({
    String? id,
    String? adId,
    UserModel? user,
    String? content,
    DateTime? createdAt,
    int? likes,
    List<CommentModel>? replies,
    String? parentCommentId,
    bool? isEdited,
    DateTime? editedAt,
  }) {
    return CommentModel(
      id: id ?? this.id,
      adId: adId ?? this.adId,
      user: user ?? this.user,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      likes: likes ?? this.likes,
      replies: replies ?? this.replies,
      parentCommentId: parentCommentId ?? this.parentCommentId,
      isEdited: isEdited ?? this.isEdited,
      editedAt: editedAt ?? this.editedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'adId': adId,
      'user': user.toJson(),
      'content': content,
      'createdAt': createdAt.toIso8601String(),
      'likes': likes,
      'replies': replies.map((reply) => reply.toJson()).toList(),
      'parentCommentId': parentCommentId,
      'isEdited': isEdited,
      'editedAt': editedAt?.toIso8601String(),
    };
  }

  factory CommentModel.fromJson(Map<String, dynamic> json) {
    return CommentModel(
      id: json['id'],
      adId: json['adId'],
      user: UserModel.fromJson(json['user']),
      content: json['content'],
      createdAt: DateTime.parse(json['createdAt']),
      likes: json['likes'] ?? 0,
      replies: (json['replies'] as List<dynamic>?)
              ?.map((reply) => CommentModel.fromJson(reply))
              .toList() ??
          [],
      parentCommentId: json['parentCommentId'],
      isEdited: json['isEdited'] ?? false,
      editedAt: json['editedAt'] != null ? DateTime.parse(json['editedAt']) : null,
    );
  }
}
