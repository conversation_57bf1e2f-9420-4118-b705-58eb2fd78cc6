// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PaymentModelAdapter extends TypeAdapter<PaymentModel> {
  @override
  final int typeId = 8;

  @override
  PaymentModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PaymentModel(
      id: fields[0] as String,
      userId: fields[1] as String,
      adId: fields[2] as String,
      amount: fields[3] as double,
      currency: fields[4] as String,
      paymentMethod: fields[5] as PaymentMethod,
      status: fields[6] as PaymentStatus,
      createdAt: fields[7] as DateTime,
      completedAt: fields[8] as DateTime?,
      transactionId: fields[9] as String?,
      failureReason: fields[10] as String?,
      metadata: (fields[11] as Map?)?.cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, PaymentModel obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.adId)
      ..writeByte(3)
      ..write(obj.amount)
      ..writeByte(4)
      ..write(obj.currency)
      ..writeByte(5)
      ..write(obj.paymentMethod)
      ..writeByte(6)
      ..write(obj.status)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.completedAt)
      ..writeByte(9)
      ..write(obj.transactionId)
      ..writeByte(10)
      ..write(obj.failureReason)
      ..writeByte(11)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentCardAdapter extends TypeAdapter<PaymentCard> {
  @override
  final int typeId = 11;

  @override
  PaymentCard read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PaymentCard(
      id: fields[0] as String,
      userId: fields[1] as String,
      last4Digits: fields[2] as String,
      brand: fields[3] as String,
      expiryMonth: fields[4] as int,
      expiryYear: fields[5] as int,
      holderName: fields[6] as String,
      isDefault: fields[7] as bool,
      createdAt: fields[8] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, PaymentCard obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.last4Digits)
      ..writeByte(3)
      ..write(obj.brand)
      ..writeByte(4)
      ..write(obj.expiryMonth)
      ..writeByte(5)
      ..write(obj.expiryYear)
      ..writeByte(6)
      ..write(obj.holderName)
      ..writeByte(7)
      ..write(obj.isDefault)
      ..writeByte(8)
      ..write(obj.createdAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentCardAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentMethodAdapter extends TypeAdapter<PaymentMethod> {
  @override
  final int typeId = 9;

  @override
  PaymentMethod read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PaymentMethod.creditCard;
      case 1:
        return PaymentMethod.debitCard;
      case 2:
        return PaymentMethod.paypal;
      case 3:
        return PaymentMethod.applePay;
      case 4:
        return PaymentMethod.googlePay;
      case 5:
        return PaymentMethod.bankTransfer;
      case 6:
        return PaymentMethod.cryptocurrency;
      default:
        return PaymentMethod.creditCard;
    }
  }

  @override
  void write(BinaryWriter writer, PaymentMethod obj) {
    switch (obj) {
      case PaymentMethod.creditCard:
        writer.writeByte(0);
        break;
      case PaymentMethod.debitCard:
        writer.writeByte(1);
        break;
      case PaymentMethod.paypal:
        writer.writeByte(2);
        break;
      case PaymentMethod.applePay:
        writer.writeByte(3);
        break;
      case PaymentMethod.googlePay:
        writer.writeByte(4);
        break;
      case PaymentMethod.bankTransfer:
        writer.writeByte(5);
        break;
      case PaymentMethod.cryptocurrency:
        writer.writeByte(6);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentMethodAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentStatusAdapter extends TypeAdapter<PaymentStatus> {
  @override
  final int typeId = 10;

  @override
  PaymentStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PaymentStatus.pending;
      case 1:
        return PaymentStatus.processing;
      case 2:
        return PaymentStatus.completed;
      case 3:
        return PaymentStatus.failed;
      case 4:
        return PaymentStatus.cancelled;
      case 5:
        return PaymentStatus.refunded;
      default:
        return PaymentStatus.pending;
    }
  }

  @override
  void write(BinaryWriter writer, PaymentStatus obj) {
    switch (obj) {
      case PaymentStatus.pending:
        writer.writeByte(0);
        break;
      case PaymentStatus.processing:
        writer.writeByte(1);
        break;
      case PaymentStatus.completed:
        writer.writeByte(2);
        break;
      case PaymentStatus.failed:
        writer.writeByte(3);
        break;
      case PaymentStatus.cancelled:
        writer.writeByte(4);
        break;
      case PaymentStatus.refunded:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
