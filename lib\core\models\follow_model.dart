import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'user_model.dart';

part 'follow_model.g.dart';

@HiveType(typeId: 15)
class FollowModel extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String followerId; // المتابِع
  
  @HiveField(2)
  final String followingId; // المتابَع
  
  @HiveField(3)
  final DateTime createdAt;
  
  @HiveField(4)
  final bool isActive;

  const FollowModel({
    required this.id,
    required this.followerId,
    required this.followingId,
    required this.createdAt,
    this.isActive = true,
  });

  @override
  List<Object> get props => [
        id,
        followerId,
        followingId,
        createdAt,
        isActive,
      ];

  FollowModel copyWith({
    String? id,
    String? followerId,
    String? followingId,
    DateTime? createdAt,
    bool? isActive,
  }) {
    return FollowModel(
      id: id ?? this.id,
      followerId: followerId ?? this.followerId,
      followingId: followingId ?? this.followingId,
      createdAt: createdAt ?? this.createdAt,
      isActive: isActive ?? this.isActive,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'followerId': followerId,
      'followingId': followingId,
      'createdAt': createdAt.toIso8601String(),
      'isActive': isActive,
    };
  }

  factory FollowModel.fromJson(Map<String, dynamic> json) {
    return FollowModel(
      id: json['id'],
      followerId: json['followerId'],
      followingId: json['followingId'],
      createdAt: DateTime.parse(json['createdAt']),
      isActive: json['isActive'] ?? true,
    );
  }
}

@HiveType(typeId: 16)
class FollowStats extends Equatable {
  @HiveField(0)
  final String userId;
  
  @HiveField(1)
  final int followersCount;
  
  @HiveField(2)
  final int followingCount;
  
  @HiveField(3)
  final DateTime lastUpdated;

  const FollowStats({
    required this.userId,
    required this.followersCount,
    required this.followingCount,
    required this.lastUpdated,
  });

  @override
  List<Object> get props => [
        userId,
        followersCount,
        followingCount,
        lastUpdated,
      ];

  FollowStats copyWith({
    String? userId,
    int? followersCount,
    int? followingCount,
    DateTime? lastUpdated,
  }) {
    return FollowStats(
      userId: userId ?? this.userId,
      followersCount: followersCount ?? this.followersCount,
      followingCount: followingCount ?? this.followingCount,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'followersCount': followersCount,
      'followingCount': followingCount,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory FollowStats.fromJson(Map<String, dynamic> json) {
    return FollowStats(
      userId: json['userId'],
      followersCount: json['followersCount'],
      followingCount: json['followingCount'],
      lastUpdated: DateTime.parse(json['lastUpdated']),
    );
  }
}

@HiveType(typeId: 17)
class FollowRequest extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String fromUserId;
  
  @HiveField(2)
  final String toUserId;
  
  @HiveField(3)
  final FollowRequestStatus status;
  
  @HiveField(4)
  final DateTime createdAt;
  
  @HiveField(5)
  final DateTime? respondedAt;
  
  @HiveField(6)
  final String? message;

  const FollowRequest({
    required this.id,
    required this.fromUserId,
    required this.toUserId,
    required this.status,
    required this.createdAt,
    this.respondedAt,
    this.message,
  });

  @override
  List<Object?> get props => [
        id,
        fromUserId,
        toUserId,
        status,
        createdAt,
        respondedAt,
        message,
      ];

  FollowRequest copyWith({
    String? id,
    String? fromUserId,
    String? toUserId,
    FollowRequestStatus? status,
    DateTime? createdAt,
    DateTime? respondedAt,
    String? message,
  }) {
    return FollowRequest(
      id: id ?? this.id,
      fromUserId: fromUserId ?? this.fromUserId,
      toUserId: toUserId ?? this.toUserId,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      respondedAt: respondedAt ?? this.respondedAt,
      message: message ?? this.message,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fromUserId': fromUserId,
      'toUserId': toUserId,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'respondedAt': respondedAt?.toIso8601String(),
      'message': message,
    };
  }

  factory FollowRequest.fromJson(Map<String, dynamic> json) {
    return FollowRequest(
      id: json['id'],
      fromUserId: json['fromUserId'],
      toUserId: json['toUserId'],
      status: FollowRequestStatus.values.firstWhere(
        (status) => status.name == json['status'],
        orElse: () => FollowRequestStatus.pending,
      ),
      createdAt: DateTime.parse(json['createdAt']),
      respondedAt: json['respondedAt'] != null ? DateTime.parse(json['respondedAt']) : null,
      message: json['message'],
    );
  }
}

@HiveType(typeId: 18)
enum FollowRequestStatus {
  @HiveField(0)
  pending,
  
  @HiveField(1)
  accepted,
  
  @HiveField(2)
  rejected,
  
  @HiveField(3)
  cancelled,
}

class FollowSuggestion extends Equatable {
  final UserModel user;
  final double score;
  final List<String> reasons;
  final int mutualFollowersCount;

  const FollowSuggestion({
    required this.user,
    required this.score,
    required this.reasons,
    this.mutualFollowersCount = 0,
  });

  @override
  List<Object> get props => [
        user,
        score,
        reasons,
        mutualFollowersCount,
      ];

  FollowSuggestion copyWith({
    UserModel? user,
    double? score,
    List<String>? reasons,
    int? mutualFollowersCount,
  }) {
    return FollowSuggestion(
      user: user ?? this.user,
      score: score ?? this.score,
      reasons: reasons ?? this.reasons,
      mutualFollowersCount: mutualFollowersCount ?? this.mutualFollowersCount,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user': user.toJson(),
      'score': score,
      'reasons': reasons,
      'mutualFollowersCount': mutualFollowersCount,
    };
  }

  factory FollowSuggestion.fromJson(Map<String, dynamic> json) {
    return FollowSuggestion(
      user: UserModel.fromJson(json['user']),
      score: json['score'].toDouble(),
      reasons: List<String>.from(json['reasons']),
      mutualFollowersCount: json['mutualFollowersCount'] ?? 0,
    );
  }
}
