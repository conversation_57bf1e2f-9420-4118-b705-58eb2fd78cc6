import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/models/notification_model.dart';
import '../widgets/notification_tile.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  
  final List<NotificationModel> notifications = [
    NotificationModel(
      id: 'notif_1',
      userId: 'current_user',
      title: 'New Ad from TechStore',
      body: 'Check out the latest electric car with amazing features!',
      type: NotificationType.newAd,
      imageUrl: 'https://images.unsplash.com/photo-1593941707882-a5bac6861d75?w=100&h=100&fit=crop',
      data: {'adId': 'ad_1'},
      isRead: false,
      createdAt: DateTime.now().subtract(const Duration(minutes: 5)),
    ),
    NotificationModel(
      id: 'notif_2',
      userId: 'current_user',
      title: 'Someone liked your comment',
      body: 'Your comment on "Summer Fashion Sale" received a like!',
      type: NotificationType.adLiked,
      isRead: false,
      createdAt: DateTime.now().subtract(const Duration(hours: 1)),
    ),
    NotificationModel(
      id: 'notif_3',
      userId: 'current_user',
      title: 'Payment Successful',
      body: 'Your payment of \$899.99 for Smartphone has been processed.',
      type: NotificationType.paymentSuccess,
      data: {'paymentId': 'payment_123', 'amount': 899.99},
      isRead: true,
      createdAt: DateTime.now().subtract(const Duration(hours: 3)),
      readAt: DateTime.now().subtract(const Duration(hours: 2)),
    ),
    NotificationModel(
      id: 'notif_4',
      userId: 'current_user',
      title: 'New Follower',
      body: 'FashionHub started following you!',
      type: NotificationType.newFollower,
      imageUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
      data: {'followerId': 'user_2'},
      isRead: true,
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      readAt: DateTime.now().subtract(const Duration(hours: 12)),
    ),
    NotificationModel(
      id: 'notif_5',
      userId: 'current_user',
      title: 'Special Promotion',
      body: 'Get 50% off on all fashion items this weekend only!',
      type: NotificationType.promotion,
      imageUrl: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=100&h=100&fit=crop',
      isRead: false,
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final unreadCount = notifications.where((n) => !n.isRead).length;
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Row(
          children: [
            const Text('Notifications'),
            if (unreadCount > 0) ...[
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  unreadCount.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
        actions: [
          if (unreadCount > 0)
            TextButton(
              onPressed: _markAllAsRead,
              child: const Text('Mark all read'),
            ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showNotificationSettings(),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('All'),
                  if (notifications.isNotEmpty) ...[
                    const SizedBox(width: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        notifications.length.toString(),
                        style: const TextStyle(fontSize: 10),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('Unread'),
                  if (unreadCount > 0) ...[
                    const SizedBox(width: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        unreadCount.toString(),
                        style: const TextStyle(
                          fontSize: 10,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            const Tab(text: 'Important'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildNotificationsList(notifications),
          _buildNotificationsList(notifications.where((n) => !n.isRead).toList()),
          _buildNotificationsList(notifications.where((n) => _isImportant(n.type)).toList()),
        ],
      ),
    );
  }

  Widget _buildNotificationsList(List<NotificationModel> notificationsList) {
    if (notificationsList.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: notificationsList.length,
      itemBuilder: (context, index) {
        final notification = notificationsList[index];
        return NotificationTile(
          notification: notification,
          onTap: () => _handleNotificationTap(notification),
          onMarkAsRead: () => _markAsRead(notification),
          onDelete: () => _deleteNotification(notification),
        ).animate(delay: Duration(milliseconds: 100 * index))
         .slideX(begin: 0.3, end: 0)
         .fadeIn();
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 80,
            color: Colors.grey[400],
          ).animate().scale(
                duration: const Duration(milliseconds: 600),
                curve: Curves.elasticOut,
              ),
          
          const SizedBox(height: 24),
          
          Text(
            'No notifications yet',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ).animate().fadeIn(
                delay: const Duration(milliseconds: 200),
                duration: const Duration(milliseconds: 600),
              ),
          
          const SizedBox(height: 12),
          
          Text(
            'We\'ll notify you when something interesting happens',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ).animate().fadeIn(
                delay: const Duration(milliseconds: 400),
                duration: const Duration(milliseconds: 600),
              ),
        ],
      ),
    );
  }

  bool _isImportant(NotificationType type) {
    return [
      NotificationType.paymentSuccess,
      NotificationType.paymentFailed,
      NotificationType.adApproved,
      NotificationType.adRejected,
      NotificationType.systemUpdate,
    ].contains(type);
  }

  void _handleNotificationTap(NotificationModel notification) {
    // تسجيل الإشعار كمقروء
    if (!notification.isRead) {
      _markAsRead(notification);
    }

    // التنقل حسب نوع الإشعار
    switch (notification.type) {
      case NotificationType.newAd:
      case NotificationType.followingNewAd:
        if (notification.data?['adId'] != null) {
          // فتح تفاصيل الإعلان
          _navigateToAd(notification.data!['adId']);
        }
        break;
      case NotificationType.newFollower:
        if (notification.data?['followerId'] != null) {
          // فتح ملف المتابع
          _navigateToProfile(notification.data!['followerId']);
        }
        break;
      case NotificationType.paymentSuccess:
      case NotificationType.paymentFailed:
        // فتح تفاصيل الدفع
        _navigateToPaymentDetails(notification.data?['paymentId']);
        break;
      default:
        // عرض تفاصيل الإشعار
        _showNotificationDetails(notification);
    }
  }

  void _markAsRead(NotificationModel notification) {
    setState(() {
      final index = notifications.indexWhere((n) => n.id == notification.id);
      if (index != -1) {
        notifications[index] = notification.copyWith(
          isRead: true,
          readAt: DateTime.now(),
        );
      }
    });
  }

  void _markAllAsRead() {
    setState(() {
      for (int i = 0; i < notifications.length; i++) {
        if (!notifications[i].isRead) {
          notifications[i] = notifications[i].copyWith(
            isRead: true,
            readAt: DateTime.now(),
          );
        }
      }
    });
  }

  void _deleteNotification(NotificationModel notification) {
    setState(() {
      notifications.removeWhere((n) => n.id == notification.id);
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Notification deleted'),
        action: SnackBarAction(
          label: 'Undo',
          onPressed: () {
            setState(() {
              notifications.add(notification);
              notifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));
            });
          },
        ),
      ),
    );
  }

  void _navigateToAd(String adId) {
    // التنقل لشاشة تفاصيل الإعلان
    print('Navigate to ad: $adId');
  }

  void _navigateToProfile(String userId) {
    // التنقل لملف المستخدم
    print('Navigate to profile: $userId');
  }

  void _navigateToPaymentDetails(String? paymentId) {
    // التنقل لتفاصيل الدفع
    print('Navigate to payment: $paymentId');
  }

  void _showNotificationDetails(NotificationModel notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(notification.title),
        content: Text(notification.body),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showNotificationSettings() {
    // فتح إعدادات الإشعارات
    print('Show notification settings');
  }
}
