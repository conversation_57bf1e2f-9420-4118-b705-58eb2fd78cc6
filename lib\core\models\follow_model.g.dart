// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'follow_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class FollowModelAdapter extends TypeAdapter<FollowModel> {
  @override
  final int typeId = 15;

  @override
  FollowModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return FollowModel(
      id: fields[0] as String,
      followerId: fields[1] as String,
      followingId: fields[2] as String,
      createdAt: fields[3] as DateTime,
      isActive: fields[4] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, FollowModel obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.followerId)
      ..writeByte(2)
      ..write(obj.followingId)
      ..writeByte(3)
      ..write(obj.createdAt)
      ..writeByte(4)
      ..write(obj.isActive);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FollowModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class FollowStatsAdapter extends TypeAdapter<FollowStats> {
  @override
  final int typeId = 16;

  @override
  FollowStats read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return FollowStats(
      userId: fields[0] as String,
      followersCount: fields[1] as int,
      followingCount: fields[2] as int,
      lastUpdated: fields[3] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, FollowStats obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.userId)
      ..writeByte(1)
      ..write(obj.followersCount)
      ..writeByte(2)
      ..write(obj.followingCount)
      ..writeByte(3)
      ..write(obj.lastUpdated);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FollowStatsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class FollowRequestAdapter extends TypeAdapter<FollowRequest> {
  @override
  final int typeId = 17;

  @override
  FollowRequest read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return FollowRequest(
      id: fields[0] as String,
      fromUserId: fields[1] as String,
      toUserId: fields[2] as String,
      status: fields[3] as FollowRequestStatus,
      createdAt: fields[4] as DateTime,
      respondedAt: fields[5] as DateTime?,
      message: fields[6] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, FollowRequest obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.fromUserId)
      ..writeByte(2)
      ..write(obj.toUserId)
      ..writeByte(3)
      ..write(obj.status)
      ..writeByte(4)
      ..write(obj.createdAt)
      ..writeByte(5)
      ..write(obj.respondedAt)
      ..writeByte(6)
      ..write(obj.message);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FollowRequestAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class FollowRequestStatusAdapter extends TypeAdapter<FollowRequestStatus> {
  @override
  final int typeId = 18;

  @override
  FollowRequestStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return FollowRequestStatus.pending;
      case 1:
        return FollowRequestStatus.accepted;
      case 2:
        return FollowRequestStatus.rejected;
      case 3:
        return FollowRequestStatus.cancelled;
      default:
        return FollowRequestStatus.pending;
    }
  }

  @override
  void write(BinaryWriter writer, FollowRequestStatus obj) {
    switch (obj) {
      case FollowRequestStatus.pending:
        writer.writeByte(0);
        break;
      case FollowRequestStatus.accepted:
        writer.writeByte(1);
        break;
      case FollowRequestStatus.rejected:
        writer.writeByte(2);
        break;
      case FollowRequestStatus.cancelled:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FollowRequestStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
