import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';

part 'analytics_model.g.dart';

@HiveType(typeId: 19)
class AdAnalytics extends Equatable {
  @HiveField(0)
  final String adId;
  
  @HiveField(1)
  final int totalViews;
  
  @HiveField(2)
  final int uniqueViews;
  
  @HiveField(3)
  final int likes;
  
  @HiveField(4)
  final int comments;
  
  @HiveField(5)
  final int shares;
  
  @HiveField(6)
  final int clicks;
  
  @HiveField(7)
  final int saves;
  
  @HiveField(8)
  final double engagementRate;
  
  @HiveField(9)
  final double clickThroughRate;
  
  @HiveField(10)
  final Map<String, int> viewsByCountry;
  
  @HiveField(11)
  final Map<String, int> viewsByAge;
  
  @HiveField(12)
  final Map<String, int> viewsByGender;
  
  @HiveField(13)
  final Map<String, int> viewsByDevice;
  
  @HiveField(14)
  final List<DailyStats> dailyStats;
  
  @HiveField(15)
  final DateTime lastUpdated;

  const AdAnalytics({
    required this.adId,
    required this.totalViews,
    required this.uniqueViews,
    required this.likes,
    required this.comments,
    required this.shares,
    required this.clicks,
    required this.saves,
    required this.engagementRate,
    required this.clickThroughRate,
    required this.viewsByCountry,
    required this.viewsByAge,
    required this.viewsByGender,
    required this.viewsByDevice,
    required this.dailyStats,
    required this.lastUpdated,
  });

  @override
  List<Object> get props => [
        adId,
        totalViews,
        uniqueViews,
        likes,
        comments,
        shares,
        clicks,
        saves,
        engagementRate,
        clickThroughRate,
        viewsByCountry,
        viewsByAge,
        viewsByGender,
        viewsByDevice,
        dailyStats,
        lastUpdated,
      ];

  AdAnalytics copyWith({
    String? adId,
    int? totalViews,
    int? uniqueViews,
    int? likes,
    int? comments,
    int? shares,
    int? clicks,
    int? saves,
    double? engagementRate,
    double? clickThroughRate,
    Map<String, int>? viewsByCountry,
    Map<String, int>? viewsByAge,
    Map<String, int>? viewsByGender,
    Map<String, int>? viewsByDevice,
    List<DailyStats>? dailyStats,
    DateTime? lastUpdated,
  }) {
    return AdAnalytics(
      adId: adId ?? this.adId,
      totalViews: totalViews ?? this.totalViews,
      uniqueViews: uniqueViews ?? this.uniqueViews,
      likes: likes ?? this.likes,
      comments: comments ?? this.comments,
      shares: shares ?? this.shares,
      clicks: clicks ?? this.clicks,
      saves: saves ?? this.saves,
      engagementRate: engagementRate ?? this.engagementRate,
      clickThroughRate: clickThroughRate ?? this.clickThroughRate,
      viewsByCountry: viewsByCountry ?? this.viewsByCountry,
      viewsByAge: viewsByAge ?? this.viewsByAge,
      viewsByGender: viewsByGender ?? this.viewsByGender,
      viewsByDevice: viewsByDevice ?? this.viewsByDevice,
      dailyStats: dailyStats ?? this.dailyStats,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  int get totalEngagements => likes + comments + shares + clicks + saves;
  
  double get saveRate => totalViews > 0 ? (saves / totalViews) * 100 : 0;
  
  double get likeRate => totalViews > 0 ? (likes / totalViews) * 100 : 0;
  
  double get commentRate => totalViews > 0 ? (comments / totalViews) * 100 : 0;
  
  double get shareRate => totalViews > 0 ? (shares / totalViews) * 100 : 0;

  Map<String, dynamic> toJson() {
    return {
      'adId': adId,
      'totalViews': totalViews,
      'uniqueViews': uniqueViews,
      'likes': likes,
      'comments': comments,
      'shares': shares,
      'clicks': clicks,
      'saves': saves,
      'engagementRate': engagementRate,
      'clickThroughRate': clickThroughRate,
      'viewsByCountry': viewsByCountry,
      'viewsByAge': viewsByAge,
      'viewsByGender': viewsByGender,
      'viewsByDevice': viewsByDevice,
      'dailyStats': dailyStats.map((stat) => stat.toJson()).toList(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory AdAnalytics.fromJson(Map<String, dynamic> json) {
    return AdAnalytics(
      adId: json['adId'],
      totalViews: json['totalViews'],
      uniqueViews: json['uniqueViews'],
      likes: json['likes'],
      comments: json['comments'],
      shares: json['shares'],
      clicks: json['clicks'],
      saves: json['saves'],
      engagementRate: json['engagementRate'].toDouble(),
      clickThroughRate: json['clickThroughRate'].toDouble(),
      viewsByCountry: Map<String, int>.from(json['viewsByCountry']),
      viewsByAge: Map<String, int>.from(json['viewsByAge']),
      viewsByGender: Map<String, int>.from(json['viewsByGender']),
      viewsByDevice: Map<String, int>.from(json['viewsByDevice']),
      dailyStats: (json['dailyStats'] as List)
          .map((stat) => DailyStats.fromJson(stat))
          .toList(),
      lastUpdated: DateTime.parse(json['lastUpdated']),
    );
  }
}

@HiveType(typeId: 20)
class DailyStats extends Equatable {
  @HiveField(0)
  final DateTime date;
  
  @HiveField(1)
  final int views;
  
  @HiveField(2)
  final int likes;
  
  @HiveField(3)
  final int comments;
  
  @HiveField(4)
  final int shares;
  
  @HiveField(5)
  final int clicks;

  const DailyStats({
    required this.date,
    required this.views,
    required this.likes,
    required this.comments,
    required this.shares,
    required this.clicks,
  });

  @override
  List<Object> get props => [
        date,
        views,
        likes,
        comments,
        shares,
        clicks,
      ];

  DailyStats copyWith({
    DateTime? date,
    int? views,
    int? likes,
    int? comments,
    int? shares,
    int? clicks,
  }) {
    return DailyStats(
      date: date ?? this.date,
      views: views ?? this.views,
      likes: likes ?? this.likes,
      comments: comments ?? this.comments,
      shares: shares ?? this.shares,
      clicks: clicks ?? this.clicks,
    );
  }

  int get totalEngagements => likes + comments + shares + clicks;
  
  double get engagementRate => views > 0 ? (totalEngagements / views) * 100 : 0;

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'views': views,
      'likes': likes,
      'comments': comments,
      'shares': shares,
      'clicks': clicks,
    };
  }

  factory DailyStats.fromJson(Map<String, dynamic> json) {
    return DailyStats(
      date: DateTime.parse(json['date']),
      views: json['views'],
      likes: json['likes'],
      comments: json['comments'],
      shares: json['shares'],
      clicks: json['clicks'],
    );
  }
}

@HiveType(typeId: 21)
class UserAnalytics extends Equatable {
  @HiveField(0)
  final String userId;
  
  @HiveField(1)
  final int totalAds;
  
  @HiveField(2)
  final int activeAds;
  
  @HiveField(3)
  final int totalViews;
  
  @HiveField(4)
  final int totalLikes;
  
  @HiveField(5)
  final int totalComments;
  
  @HiveField(6)
  final int totalShares;
  
  @HiveField(7)
  final int totalFollowers;
  
  @HiveField(8)
  final int totalFollowing;
  
  @HiveField(9)
  final double averageEngagementRate;
  
  @HiveField(10)
  final String topPerformingAdId;
  
  @HiveField(11)
  final List<MonthlyStats> monthlyStats;
  
  @HiveField(12)
  final DateTime lastUpdated;

  const UserAnalytics({
    required this.userId,
    required this.totalAds,
    required this.activeAds,
    required this.totalViews,
    required this.totalLikes,
    required this.totalComments,
    required this.totalShares,
    required this.totalFollowers,
    required this.totalFollowing,
    required this.averageEngagementRate,
    required this.topPerformingAdId,
    required this.monthlyStats,
    required this.lastUpdated,
  });

  @override
  List<Object> get props => [
        userId,
        totalAds,
        activeAds,
        totalViews,
        totalLikes,
        totalComments,
        totalShares,
        totalFollowers,
        totalFollowing,
        averageEngagementRate,
        topPerformingAdId,
        monthlyStats,
        lastUpdated,
      ];

  int get totalEngagements => totalLikes + totalComments + totalShares;
  
  double get averageViewsPerAd => totalAds > 0 ? totalViews / totalAds : 0;
  
  double get followerGrowthRate {
    if (monthlyStats.length < 2) return 0;
    final current = monthlyStats.last.followers;
    final previous = monthlyStats[monthlyStats.length - 2].followers;
    return previous > 0 ? ((current - previous) / previous) * 100 : 0;
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'totalAds': totalAds,
      'activeAds': activeAds,
      'totalViews': totalViews,
      'totalLikes': totalLikes,
      'totalComments': totalComments,
      'totalShares': totalShares,
      'totalFollowers': totalFollowers,
      'totalFollowing': totalFollowing,
      'averageEngagementRate': averageEngagementRate,
      'topPerformingAdId': topPerformingAdId,
      'monthlyStats': monthlyStats.map((stat) => stat.toJson()).toList(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory UserAnalytics.fromJson(Map<String, dynamic> json) {
    return UserAnalytics(
      userId: json['userId'],
      totalAds: json['totalAds'],
      activeAds: json['activeAds'],
      totalViews: json['totalViews'],
      totalLikes: json['totalLikes'],
      totalComments: json['totalComments'],
      totalShares: json['totalShares'],
      totalFollowers: json['totalFollowers'],
      totalFollowing: json['totalFollowing'],
      averageEngagementRate: json['averageEngagementRate'].toDouble(),
      topPerformingAdId: json['topPerformingAdId'],
      monthlyStats: (json['monthlyStats'] as List)
          .map((stat) => MonthlyStats.fromJson(stat))
          .toList(),
      lastUpdated: DateTime.parse(json['lastUpdated']),
    );
  }
}

@HiveType(typeId: 22)
class MonthlyStats extends Equatable {
  @HiveField(0)
  final DateTime month;
  
  @HiveField(1)
  final int views;
  
  @HiveField(2)
  final int likes;
  
  @HiveField(3)
  final int comments;
  
  @HiveField(4)
  final int shares;
  
  @HiveField(5)
  final int followers;
  
  @HiveField(6)
  final int newAds;

  const MonthlyStats({
    required this.month,
    required this.views,
    required this.likes,
    required this.comments,
    required this.shares,
    required this.followers,
    required this.newAds,
  });

  @override
  List<Object> get props => [
        month,
        views,
        likes,
        comments,
        shares,
        followers,
        newAds,
      ];

  int get totalEngagements => likes + comments + shares;

  Map<String, dynamic> toJson() {
    return {
      'month': month.toIso8601String(),
      'views': views,
      'likes': likes,
      'comments': comments,
      'shares': shares,
      'followers': followers,
      'newAds': newAds,
    };
  }

  factory MonthlyStats.fromJson(Map<String, dynamic> json) {
    return MonthlyStats(
      month: DateTime.parse(json['month']),
      views: json['views'],
      likes: json['likes'],
      comments: json['comments'],
      shares: json['shares'],
      followers: json['followers'],
      newAds: json['newAds'],
    );
  }
}
