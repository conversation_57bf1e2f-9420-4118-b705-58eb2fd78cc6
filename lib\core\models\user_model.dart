import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';

part 'user_model.g.dart';

@HiveType(typeId: 0)
class UserModel extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String username;
  
  @HiveField(2)
  final String email;
  
  @HiveField(3)
  final String? profileImageUrl;
  
  @HiveField(4)
  final String? bio;
  
  @HiveField(5)
  final int followersCount;
  
  @HiveField(6)
  final int followingCount;
  
  @HiveField(7)
  final bool isVerified;
  
  @HiveField(8)
  final DateTime createdAt;
  
  @HiveField(9)
  final DateTime lastActiveAt;
  
  @HiveField(10)
  final UserType userType;

  const UserModel({
    required this.id,
    required this.username,
    required this.email,
    this.profileImageUrl,
    this.bio,
    this.followersCount = 0,
    this.followingCount = 0,
    this.isVerified = false,
    required this.createdAt,
    required this.lastActiveAt,
    this.userType = UserType.regular,
  });

  @override
  List<Object?> get props => [
        id,
        username,
        email,
        profileImageUrl,
        bio,
        followersCount,
        followingCount,
        isVerified,
        createdAt,
        lastActiveAt,
        userType,
      ];

  UserModel copyWith({
    String? id,
    String? username,
    String? email,
    String? profileImageUrl,
    String? bio,
    int? followersCount,
    int? followingCount,
    bool? isVerified,
    DateTime? createdAt,
    DateTime? lastActiveAt,
    UserType? userType,
  }) {
    return UserModel(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      bio: bio ?? this.bio,
      followersCount: followersCount ?? this.followersCount,
      followingCount: followingCount ?? this.followingCount,
      isVerified: isVerified ?? this.isVerified,
      createdAt: createdAt ?? this.createdAt,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
      userType: userType ?? this.userType,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'profileImageUrl': profileImageUrl,
      'bio': bio,
      'followersCount': followersCount,
      'followingCount': followingCount,
      'isVerified': isVerified,
      'createdAt': createdAt.toIso8601String(),
      'lastActiveAt': lastActiveAt.toIso8601String(),
      'userType': userType.name,
    };
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      username: json['username'],
      email: json['email'],
      profileImageUrl: json['profileImageUrl'],
      bio: json['bio'],
      followersCount: json['followersCount'] ?? 0,
      followingCount: json['followingCount'] ?? 0,
      isVerified: json['isVerified'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      lastActiveAt: DateTime.parse(json['lastActiveAt']),
      userType: UserType.values.firstWhere(
        (type) => type.name == json['userType'],
        orElse: () => UserType.regular,
      ),
    );
  }
}

@HiveType(typeId: 1)
enum UserType {
  @HiveField(0)
  regular,
  
  @HiveField(1)
  advertiser,
  
  @HiveField(2)
  admin,
}
