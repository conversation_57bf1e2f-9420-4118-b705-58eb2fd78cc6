name: storyad
description: A new Flutter project.

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=2.17.0 <3.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI & Design
  cupertino_icons: ^1.0.8
  google_fonts: ^6.1.0
  flutter_animate: ^4.2.0
  shimmer: ^3.0.0
  cached_network_image: ^3.3.0
  font_awesome_flutter: ^10.7.0

  # State Management
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5

  # Storage & Data
  shared_preferences: ^2.3.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Network & API
  dio: ^5.3.2
  connectivity_plus: ^5.0.1

  # Utils
  intl: ^0.19.0
  uuid: ^4.1.0

  # Video & Media
  video_player: ^2.8.1

  # Navigation
  go_router: ^12.1.1

  # Animations
  lottie: ^2.7.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  hive_generator: ^2.0.1
  build_runner: ^2.4.7

flutter:
  generate: true # لتوليد ملفات الترجمة
  uses-material-design: true
  assets:
    - assets/
    - assets/fonts/
    - assets/images/
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
        - asset: assets/fonts/Roboto-Italic.ttf
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Regular.ttf
        - asset: assets/fonts/Poppins-Bold.ttf