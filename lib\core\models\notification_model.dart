import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';

part 'notification_model.g.dart';

@HiveType(typeId: 12)
class NotificationModel extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final String title;
  
  @HiveField(3)
  final String body;
  
  @HiveField(4)
  final NotificationType type;
  
  @HiveField(5)
  final String? imageUrl;
  
  @HiveField(6)
  final Map<String, dynamic>? data;
  
  @HiveField(7)
  final bool isRead;
  
  @HiveField(8)
  final DateTime createdAt;
  
  @HiveField(9)
  final DateTime? readAt;

  const NotificationModel({
    required this.id,
    required this.userId,
    required this.title,
    required this.body,
    required this.type,
    this.imageUrl,
    this.data,
    this.isRead = false,
    required this.createdAt,
    this.readAt,
  });

  @override
  List<Object?> get props => [
        id,
        userId,
        title,
        body,
        type,
        imageUrl,
        data,
        isRead,
        createdAt,
        readAt,
      ];

  NotificationModel copyWith({
    String? id,
    String? userId,
    String? title,
    String? body,
    NotificationType? type,
    String? imageUrl,
    Map<String, dynamic>? data,
    bool? isRead,
    DateTime? createdAt,
    DateTime? readAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      imageUrl: imageUrl ?? this.imageUrl,
      data: data ?? this.data,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'title': title,
      'body': body,
      'type': type.name,
      'imageUrl': imageUrl,
      'data': data,
      'isRead': isRead,
      'createdAt': createdAt.toIso8601String(),
      'readAt': readAt?.toIso8601String(),
    };
  }

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'],
      userId: json['userId'],
      title: json['title'],
      body: json['body'],
      type: NotificationType.values.firstWhere(
        (type) => type.name == json['type'],
        orElse: () => NotificationType.general,
      ),
      imageUrl: json['imageUrl'],
      data: json['data'] != null ? Map<String, dynamic>.from(json['data']) : null,
      isRead: json['isRead'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      readAt: json['readAt'] != null ? DateTime.parse(json['readAt']) : null,
    );
  }
}

@HiveType(typeId: 13)
enum NotificationType {
  @HiveField(0)
  general,
  
  @HiveField(1)
  newAd,
  
  @HiveField(2)
  adLiked,
  
  @HiveField(3)
  adCommented,
  
  @HiveField(4)
  adShared,
  
  @HiveField(5)
  newFollower,
  
  @HiveField(6)
  followingNewAd,
  
  @HiveField(7)
  paymentSuccess,
  
  @HiveField(8)
  paymentFailed,
  
  @HiveField(9)
  adExpired,
  
  @HiveField(10)
  adApproved,
  
  @HiveField(11)
  adRejected,
  
  @HiveField(12)
  message,
  
  @HiveField(13)
  promotion,
  
  @HiveField(14)
  systemUpdate,
}

@HiveType(typeId: 14)
class NotificationSettings extends Equatable {
  @HiveField(0)
  final String userId;
  
  @HiveField(1)
  final bool pushNotifications;
  
  @HiveField(2)
  final bool emailNotifications;
  
  @HiveField(3)
  final bool smsNotifications;
  
  @HiveField(4)
  final bool newAdNotifications;
  
  @HiveField(5)
  final bool likeNotifications;
  
  @HiveField(6)
  final bool commentNotifications;
  
  @HiveField(7)
  final bool followNotifications;
  
  @HiveField(8)
  final bool paymentNotifications;
  
  @HiveField(9)
  final bool promotionNotifications;
  
  @HiveField(10)
  final bool systemNotifications;
  
  @HiveField(11)
  final TimeOfDay? quietHoursStart;
  
  @HiveField(12)
  final TimeOfDay? quietHoursEnd;
  
  @HiveField(13)
  final DateTime updatedAt;

  const NotificationSettings({
    required this.userId,
    this.pushNotifications = true,
    this.emailNotifications = true,
    this.smsNotifications = false,
    this.newAdNotifications = true,
    this.likeNotifications = true,
    this.commentNotifications = true,
    this.followNotifications = true,
    this.paymentNotifications = true,
    this.promotionNotifications = false,
    this.systemNotifications = true,
    this.quietHoursStart,
    this.quietHoursEnd,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
        userId,
        pushNotifications,
        emailNotifications,
        smsNotifications,
        newAdNotifications,
        likeNotifications,
        commentNotifications,
        followNotifications,
        paymentNotifications,
        promotionNotifications,
        systemNotifications,
        quietHoursStart,
        quietHoursEnd,
        updatedAt,
      ];

  NotificationSettings copyWith({
    String? userId,
    bool? pushNotifications,
    bool? emailNotifications,
    bool? smsNotifications,
    bool? newAdNotifications,
    bool? likeNotifications,
    bool? commentNotifications,
    bool? followNotifications,
    bool? paymentNotifications,
    bool? promotionNotifications,
    bool? systemNotifications,
    TimeOfDay? quietHoursStart,
    TimeOfDay? quietHoursEnd,
    DateTime? updatedAt,
  }) {
    return NotificationSettings(
      userId: userId ?? this.userId,
      pushNotifications: pushNotifications ?? this.pushNotifications,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      smsNotifications: smsNotifications ?? this.smsNotifications,
      newAdNotifications: newAdNotifications ?? this.newAdNotifications,
      likeNotifications: likeNotifications ?? this.likeNotifications,
      commentNotifications: commentNotifications ?? this.commentNotifications,
      followNotifications: followNotifications ?? this.followNotifications,
      paymentNotifications: paymentNotifications ?? this.paymentNotifications,
      promotionNotifications: promotionNotifications ?? this.promotionNotifications,
      systemNotifications: systemNotifications ?? this.systemNotifications,
      quietHoursStart: quietHoursStart ?? this.quietHoursStart,
      quietHoursEnd: quietHoursEnd ?? this.quietHoursEnd,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool isInQuietHours() {
    if (quietHoursStart == null || quietHoursEnd == null) return false;
    
    final now = TimeOfDay.now();
    final start = quietHoursStart!;
    final end = quietHoursEnd!;
    
    // Handle overnight quiet hours (e.g., 22:00 to 08:00)
    if (start.hour > end.hour || (start.hour == end.hour && start.minute > end.minute)) {
      return (now.hour > start.hour || (now.hour == start.hour && now.minute >= start.minute)) ||
             (now.hour < end.hour || (now.hour == end.hour && now.minute <= end.minute));
    }
    
    // Handle same-day quiet hours (e.g., 12:00 to 14:00)
    return (now.hour > start.hour || (now.hour == start.hour && now.minute >= start.minute)) &&
           (now.hour < end.hour || (now.hour == end.hour && now.minute <= end.minute));
  }
}

class TimeOfDay {
  final int hour;
  final int minute;

  const TimeOfDay({required this.hour, required this.minute});

  factory TimeOfDay.now() {
    final now = DateTime.now();
    return TimeOfDay(hour: now.hour, minute: now.minute);
  }

  @override
  String toString() {
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TimeOfDay && other.hour == hour && other.minute == minute;
  }

  @override
  int get hashCode => hour.hashCode ^ minute.hashCode;
}
