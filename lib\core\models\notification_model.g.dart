// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class NotificationModelAdapter extends TypeAdapter<NotificationModel> {
  @override
  final int typeId = 12;

  @override
  NotificationModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return NotificationModel(
      id: fields[0] as String,
      userId: fields[1] as String,
      title: fields[2] as String,
      body: fields[3] as String,
      type: fields[4] as NotificationType,
      imageUrl: fields[5] as String?,
      data: (fields[6] as Map?)?.cast<String, dynamic>(),
      isRead: fields[7] as bool,
      createdAt: fields[8] as DateTime,
      readAt: fields[9] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, NotificationModel obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.title)
      ..writeByte(3)
      ..write(obj.body)
      ..writeByte(4)
      ..write(obj.type)
      ..writeByte(5)
      ..write(obj.imageUrl)
      ..writeByte(6)
      ..write(obj.data)
      ..writeByte(7)
      ..write(obj.isRead)
      ..writeByte(8)
      ..write(obj.createdAt)
      ..writeByte(9)
      ..write(obj.readAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NotificationModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class NotificationSettingsAdapter extends TypeAdapter<NotificationSettings> {
  @override
  final int typeId = 14;

  @override
  NotificationSettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return NotificationSettings(
      userId: fields[0] as String,
      pushNotifications: fields[1] as bool,
      emailNotifications: fields[2] as bool,
      smsNotifications: fields[3] as bool,
      newAdNotifications: fields[4] as bool,
      likeNotifications: fields[5] as bool,
      commentNotifications: fields[6] as bool,
      followNotifications: fields[7] as bool,
      paymentNotifications: fields[8] as bool,
      promotionNotifications: fields[9] as bool,
      systemNotifications: fields[10] as bool,
      quietHoursStart: fields[11] as TimeOfDay?,
      quietHoursEnd: fields[12] as TimeOfDay?,
      updatedAt: fields[13] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, NotificationSettings obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.userId)
      ..writeByte(1)
      ..write(obj.pushNotifications)
      ..writeByte(2)
      ..write(obj.emailNotifications)
      ..writeByte(3)
      ..write(obj.smsNotifications)
      ..writeByte(4)
      ..write(obj.newAdNotifications)
      ..writeByte(5)
      ..write(obj.likeNotifications)
      ..writeByte(6)
      ..write(obj.commentNotifications)
      ..writeByte(7)
      ..write(obj.followNotifications)
      ..writeByte(8)
      ..write(obj.paymentNotifications)
      ..writeByte(9)
      ..write(obj.promotionNotifications)
      ..writeByte(10)
      ..write(obj.systemNotifications)
      ..writeByte(11)
      ..write(obj.quietHoursStart)
      ..writeByte(12)
      ..write(obj.quietHoursEnd)
      ..writeByte(13)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NotificationSettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class NotificationTypeAdapter extends TypeAdapter<NotificationType> {
  @override
  final int typeId = 13;

  @override
  NotificationType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return NotificationType.general;
      case 1:
        return NotificationType.newAd;
      case 2:
        return NotificationType.adLiked;
      case 3:
        return NotificationType.adCommented;
      case 4:
        return NotificationType.adShared;
      case 5:
        return NotificationType.newFollower;
      case 6:
        return NotificationType.followingNewAd;
      case 7:
        return NotificationType.paymentSuccess;
      case 8:
        return NotificationType.paymentFailed;
      case 9:
        return NotificationType.adExpired;
      case 10:
        return NotificationType.adApproved;
      case 11:
        return NotificationType.adRejected;
      case 12:
        return NotificationType.message;
      case 13:
        return NotificationType.promotion;
      case 14:
        return NotificationType.systemUpdate;
      default:
        return NotificationType.general;
    }
  }

  @override
  void write(BinaryWriter writer, NotificationType obj) {
    switch (obj) {
      case NotificationType.general:
        writer.writeByte(0);
        break;
      case NotificationType.newAd:
        writer.writeByte(1);
        break;
      case NotificationType.adLiked:
        writer.writeByte(2);
        break;
      case NotificationType.adCommented:
        writer.writeByte(3);
        break;
      case NotificationType.adShared:
        writer.writeByte(4);
        break;
      case NotificationType.newFollower:
        writer.writeByte(5);
        break;
      case NotificationType.followingNewAd:
        writer.writeByte(6);
        break;
      case NotificationType.paymentSuccess:
        writer.writeByte(7);
        break;
      case NotificationType.paymentFailed:
        writer.writeByte(8);
        break;
      case NotificationType.adExpired:
        writer.writeByte(9);
        break;
      case NotificationType.adApproved:
        writer.writeByte(10);
        break;
      case NotificationType.adRejected:
        writer.writeByte(11);
        break;
      case NotificationType.message:
        writer.writeByte(12);
        break;
      case NotificationType.promotion:
        writer.writeByte(13);
        break;
      case NotificationType.systemUpdate:
        writer.writeByte(14);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NotificationTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
