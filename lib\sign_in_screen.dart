import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:shared_preferences/shared_preferences.dart'; // استيراد SharedPreferences
import 'package:flutter_gen/gen_l10n/app_localizations.dart'; // استيراد الترجمة
import 'package:font_awesome_flutter/font_awesome_flutter.dart'; // استيراد FontAwesome
import 'sign_up_screen.dart'; //
import 'presentation/screens/main_navigation_screen.dart'; // استيراد الشاشة الرئيسية

class SignInScreen extends StatefulWidget {
  const SignInScreen({super.key});

  @override
  _SignInScreenState createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen> {
  bool _obscureText = true; // للتحكم في إخفاء/عرض كلمة المرور
  bool _rememberMe = false; // لخيار "تذكرني"

  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadLastEmail();
  }

  Future<void> _loadLastEmail() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? lastEmail = prefs.getString('lastEmail');
    if (lastEmail != null) {
      _emailController.text = lastEmail;
      _rememberMe = true;
      setState(() {});
    }
  }

  Future<void> _signIn() async {
    final localizations = AppLocalizations.of(context)!;

    // التحقق من أن الحقول ليست فارغة
    if (_emailController.text.isEmpty || _passwordController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(localizations.pleaseFillFields)),
      );
      return;
    }

    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? storedEmail = prefs.getString('email');
    String? storedPassword = prefs.getString('password');

    if (storedEmail == null || storedPassword == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(localizations.noUserFound)),
      );
      return;
    }

    if (_emailController.text == storedEmail && _passwordController.text == storedPassword) {
      if (_rememberMe) {
        await prefs.setString('lastEmail', _emailController.text);
      }
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => const MainNavigationScreen()),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(localizations.invalidCredentials)),
      );
    }
  }

  void _signInWithGoogle() {
    final localizations = AppLocalizations.of(context)!;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(localizations.signInWithGoogle)),
    );
  }

  void _signInWithApple() {
    final localizations = AppLocalizations.of(context)!;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(localizations.signInWithApple)),
    );
  }

  void _signInWithX() {
    final localizations = AppLocalizations.of(context)!;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(localizations.signInWithX)),
    );
  }

  void _signInWithFacebook() {
    final localizations = AppLocalizations.of(context)!;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(localizations.signInWithFacebook)),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Colors.blue[300]!, Colors.blue[600]!],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 30.0, vertical: 20.0),
            child: SingleChildScrollView(
              child: ConstrainedBox(
                constraints: BoxConstraints(minHeight: MediaQuery.of(context).size.height - 60),
                child: IntrinsicHeight(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(height: 40),
                      Text(localizations.welcomeBack, style: const TextStyle(fontSize: 32, fontWeight: FontWeight.bold, color: Colors.white, fontFamily: 'Roboto')),
                      const SizedBox(height: 40),

                      TextField(
                        controller: _emailController,
                        decoration: InputDecoration(
                          filled: true,
                          fillColor: Colors.white.withOpacity(0.9),
                          hintText: localizations.email,
                          hintStyle: const TextStyle(color: Colors.grey),
                          prefixIcon: const Icon(Icons.email, color: Colors.grey),
                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(10), borderSide: BorderSide.none),
                        ),
                        keyboardType: TextInputType.emailAddress,
                      ),
                      const SizedBox(height: 20),

                      TextField(
                        controller: _passwordController,
                        decoration: InputDecoration(
                          filled: true,
                          fillColor: Colors.white.withOpacity(0.9),
                          hintText: localizations.password,
                          hintStyle: const TextStyle(color: Colors.grey),
                          prefixIcon: const Icon(Icons.lock, color: Colors.grey),
                          suffixIcon: IconButton(
                            icon: Icon(_obscureText ? Icons.visibility : Icons.visibility_off, color: Colors.grey),
                            onPressed: () => setState(() => _obscureText = !_obscureText),
                          ),
                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(10), borderSide: BorderSide.none),
                        ),
                        obscureText: _obscureText,
                      ),
                      const SizedBox(height: 10),

                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Checkbox(
                                value: _rememberMe,
                                onChanged: (value) => setState(() => _rememberMe = value ?? false),
                                activeColor: Colors.grey,
                                checkColor: Colors.white,
                                side: const BorderSide(color: Colors.grey),
                                fillColor: MaterialStateProperty.resolveWith<Color?>((states) {
                                  if (states.contains(MaterialState.selected)) {
                                    return Colors.grey;
                                  }
                                  return Colors.white.withOpacity(0.9);
                                }),
                              ),
                              Text(
                                localizations.rememberMe,
                                style: const TextStyle(
                                  color: Colors.white70,
                                  fontFamily: 'Roboto',
                                ),
                              ),
                            ],
                          ),
                          TextButton(
                            onPressed: () {},
                            child: Text(localizations.forgotPassword, style: const TextStyle(color: Colors.white70, fontFamily: 'Roboto')),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),

                      ElevatedButton(
                        onPressed: _signIn,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: Colors.black,
                          minimumSize: const Size(double.infinity, 50),
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                        ),
                        child: Text(localizations.login, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, fontFamily: 'Roboto')),
                      ),
                      const SizedBox(height: 20),

                      Row(
                        children: [
                          Expanded(child: Divider(color: Colors.white.withOpacity(0.5), thickness: 1)),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            child: Text(localizations.or, style: const TextStyle(color: Colors.white, fontSize: 16, fontFamily: 'Roboto')),
                          ),
                          Expanded(child: Divider(color: Colors.white.withOpacity(0.5), thickness: 1)),
                        ],
                      ),
                      const SizedBox(height: 20),

                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          IconButton(
                            icon: const FaIcon(FontAwesomeIcons.facebookF, size: 30, color: Colors.white),
                            onPressed: _signInWithFacebook,
                          ),
                          const SizedBox(width: 15),
                          IconButton(
                            icon: const FaIcon(FontAwesomeIcons.xTwitter, size: 30, color: Colors.white),
                            onPressed: _signInWithX,
                          ),
                          const SizedBox(width: 15),
                          IconButton(
                            icon: const FaIcon(FontAwesomeIcons.apple, size: 36, color: Colors.white),
                            onPressed: _signInWithApple,
                          ),
                          const SizedBox(width: 15),
                          IconButton(
                            icon: const FaIcon(FontAwesomeIcons.google, size: 30, color: Colors.white),
                            onPressed: _signInWithGoogle,
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),

                      RichText(
                        text: TextSpan(
                          style: const TextStyle(fontFamily: 'Roboto'),
                          children: [
                            TextSpan(text: localizations.dontHaveAccount, style: const TextStyle(color: Colors.white70)),
                            TextSpan(
                              text: localizations.signUp,
                              style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                              recognizer: TapGestureRecognizer()..onTap = () => Navigator.push(context, MaterialPageRoute(builder: (_) => const SignUpScreen())),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}