import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../core/models/ad_model.dart';
import '../../features/ads/bloc/ads_bloc.dart';
import '../widgets/explore_category_card.dart';
import '../widgets/trending_ads_section.dart';

class ExploreScreen extends StatefulWidget {
  const ExploreScreen({super.key});

  @override
  State<ExploreScreen> createState() => _ExploreScreenState();
}

class _ExploreScreenState extends State<ExploreScreen> {
  final TextEditingController _searchController = TextEditingController();

  final List<ExploreCategory> categories = [
    ExploreCategory(
      name: 'Cars',
      icon: Icons.directions_car,
      color: Colors.red,
      category: AdCategory.cars,
    ),
    ExploreCategory(
      name: 'Fashion',
      icon: Icons.checkroom,
      color: Colors.pink,
      category: AdCategory.fashion,
    ),
    ExploreCategory(
      name: 'Technology',
      icon: Icons.smartphone,
      color: Colors.blue,
      category: AdCategory.technology,
    ),
    ExploreCategory(
      name: 'Food',
      icon: Icons.restaurant,
      color: Colors.orange,
      category: AdCategory.food,
    ),
    ExploreCategory(
      name: 'Beauty',
      icon: Icons.face,
      color: Colors.purple,
      category: AdCategory.beauty,
    ),
    ExploreCategory(
      name: 'Sports',
      icon: Icons.sports_soccer,
      color: Colors.green,
      category: AdCategory.sports,
    ),
  ];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // شريط البحث
            Container(
              padding: const EdgeInsets.all(16),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search for products, brands...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: IconButton(
                    icon: const Icon(Icons.tune),
                    onPressed: () {
                      // فتح فلاتر البحث
                    },
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(25),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.grey[100],
                ),
                onSubmitted: (query) {
                  if (query.isNotEmpty) {
                    context.read<AdsBloc>().add(SearchAds(query));
                  }
                },
              ),
            ),

            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // العنوان الرئيسي
                    Text(
                      'Explore Categories',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ).animate().fadeIn(duration: const Duration(milliseconds: 600)),

                    const SizedBox(height: 16),

                    // شبكة الفئات
                    GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 1.2,
                        crossAxisSpacing: 12,
                        mainAxisSpacing: 12,
                      ),
                      itemCount: categories.length,
                      itemBuilder: (context, index) {
                        return ExploreCategoryCard(
                          category: categories[index],
                          onTap: () {
                            context.read<AdsBloc>().add(
                              FilterAdsByCategory([categories[index].category]),
                            );
                            // العودة لشاشة الإعلانات
                            DefaultTabController.of(context)?.animateTo(0);
                          },
                        ).animate(delay: Duration(milliseconds: 100 * index))
                         .slideX(begin: 0.3, end: 0)
                         .fadeIn();
                      },
                    ),

                    const SizedBox(height: 32),

                    // قسم الإعلانات الرائجة
                    const TrendingAdsSection(),

                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ExploreCategory {
  final String name;
  final IconData icon;
  final Color color;
  final AdCategory category;

  ExploreCategory({
    required this.name,
    required this.icon,
    required this.color,
    required this.category,
  });
}
