import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/models/payment_model.dart';
import '../../core/models/ad_model.dart';
import '../widgets/payment_method_card.dart';
import '../widgets/payment_summary_card.dart';

class PaymentScreen extends StatefulWidget {
  final AdModel ad;
  final double amount;

  const PaymentScreen({
    super.key,
    required this.ad,
    required this.amount,
  });

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  PaymentMethod selectedPaymentMethod = PaymentMethod.creditCard;
  bool isProcessing = false;
  
  final List<PaymentCard> savedCards = [
    PaymentCard(
      id: 'card_1',
      userId: 'current_user',
      last4Digits: '4242',
      brand: 'Visa',
      expiryMonth: 12,
      expiryYear: 2025,
      holderName: '<PERSON>',
      isDefault: true,
      createdAt: DateTime.now(),
    ),
    PaymentCard(
      id: 'card_2',
      userId: 'current_user',
      last4Digits: '5555',
      brand: 'Mastercard',
      expiryMonth: 8,
      expiryYear: 2026,
      holderName: 'John Doe',
      isDefault: false,
      createdAt: DateTime.now(),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Payment'),
        elevation: 0,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // ملخص الطلب
                  PaymentSummaryCard(
                    ad: widget.ad,
                    amount: widget.amount,
                  ).animate().slideY(
                        begin: 0.3,
                        end: 0,
                        duration: const Duration(milliseconds: 600),
                      ),
                  
                  const SizedBox(height: 24),
                  
                  // طرق الدفع
                  Text(
                    'Payment Methods',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ).animate().fadeIn(
                        delay: const Duration(milliseconds: 200),
                        duration: const Duration(milliseconds: 600),
                      ),
                  
                  const SizedBox(height: 16),
                  
                  // البطاقات المحفوظة
                  if (savedCards.isNotEmpty) ...[
                    Text(
                      'Saved Cards',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    
                    ...savedCards.map((card) => PaymentMethodCard(
                          paymentMethod: PaymentMethod.creditCard,
                          card: card,
                          isSelected: selectedPaymentMethod == PaymentMethod.creditCard,
                          onTap: () {
                            setState(() {
                              selectedPaymentMethod = PaymentMethod.creditCard;
                            });
                          },
                        ).animate(delay: Duration(milliseconds: 100 * savedCards.indexOf(card)))
                         .slideX(begin: 0.3, end: 0)
                         .fadeIn()),
                    
                    const SizedBox(height: 16),
                  ],
                  
                  // طرق دفع أخرى
                  Text(
                    'Other Payment Methods',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  PaymentMethodCard(
                    paymentMethod: PaymentMethod.paypal,
                    isSelected: selectedPaymentMethod == PaymentMethod.paypal,
                    onTap: () {
                      setState(() {
                        selectedPaymentMethod = PaymentMethod.paypal;
                      });
                    },
                  ).animate(delay: const Duration(milliseconds: 400))
                   .slideX(begin: 0.3, end: 0)
                   .fadeIn(),
                  
                  const SizedBox(height: 12),
                  
                  PaymentMethodCard(
                    paymentMethod: PaymentMethod.applePay,
                    isSelected: selectedPaymentMethod == PaymentMethod.applePay,
                    onTap: () {
                      setState(() {
                        selectedPaymentMethod = PaymentMethod.applePay;
                      });
                    },
                  ).animate(delay: const Duration(milliseconds: 500))
                   .slideX(begin: 0.3, end: 0)
                   .fadeIn(),
                  
                  const SizedBox(height: 12),
                  
                  PaymentMethodCard(
                    paymentMethod: PaymentMethod.googlePay,
                    isSelected: selectedPaymentMethod == PaymentMethod.googlePay,
                    onTap: () {
                      setState(() {
                        selectedPaymentMethod = PaymentMethod.googlePay;
                      });
                    },
                  ).animate(delay: const Duration(milliseconds: 600))
                   .slideX(begin: 0.3, end: 0)
                   .fadeIn(),
                  
                  const SizedBox(height: 24),
                  
                  // معلومات الأمان
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.blue.withOpacity(0.3)),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.security,
                          color: theme.primaryColor,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Secure Payment',
                                style: theme.textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: theme.primaryColor,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Your payment information is encrypted and secure.',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ).animate(delay: const Duration(milliseconds: 700))
                   .slideY(begin: 0.3, end: 0)
                   .fadeIn(),
                ],
              ),
            ),
          ),
          
          // زر الدفع
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.scaffoldBackgroundColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: SafeArea(
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: isProcessing ? null : _processPayment,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: isProcessing
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          'Pay \$${widget.amount.toStringAsFixed(2)}',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
            ),
          ).animate(delay: const Duration(milliseconds: 800))
           .slideY(begin: 1, end: 0)
           .fadeIn(),
        ],
      ),
    );
  }

  Future<void> _processPayment() async {
    setState(() {
      isProcessing = true;
    });

    try {
      // محاكاة معالجة الدفع
      await Future.delayed(const Duration(seconds: 2));
      
      // إنشاء نموذج الدفع
      final payment = PaymentModel(
        id: 'payment_${DateTime.now().millisecondsSinceEpoch}',
        userId: 'current_user',
        adId: widget.ad.id,
        amount: widget.amount,
        currency: 'USD',
        paymentMethod: selectedPaymentMethod,
        status: PaymentStatus.completed,
        createdAt: DateTime.now(),
        completedAt: DateTime.now(),
        transactionId: 'txn_${DateTime.now().millisecondsSinceEpoch}',
      );

      if (mounted) {
        // عرض رسالة نجاح
        _showSuccessDialog(payment);
      }
    } catch (e) {
      if (mounted) {
        // عرض رسالة خطأ
        _showErrorDialog(e.toString());
      }
    } finally {
      if (mounted) {
        setState(() {
          isProcessing = false;
        });
      }
    }
  }

  void _showSuccessDialog(PaymentModel payment) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 64,
            ).animate().scale(
                  duration: const Duration(milliseconds: 600),
                  curve: Curves.elasticOut,
                ),
            const SizedBox(height: 16),
            Text(
              'Payment Successful!',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Transaction ID: ${payment.transactionId}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop(); // إغلاق الحوار
                  Navigator.of(context).pop(); // العودة للشاشة السابقة
                },
                child: const Text('Done'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Text('Payment Failed'),
        content: Text(error),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
