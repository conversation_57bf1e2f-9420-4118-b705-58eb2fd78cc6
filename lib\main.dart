import 'package:flutter/material.dart';
import 'dart:async';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'sign_in_screen.dart';
import 'main_navigation_screen.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  Locale _locale = const Locale('en'); // اللغة الافتراضية (سيتم تحديثها لاحقًا)

  void setLocale(Locale locale) {
    setState(() {
      _locale = locale;
    });
  }

  @override
  void initState() {
    super.initState();
    getLocale().then((locale) => setLocale(locale));
  }

  Future<Locale> getLocale() async {
    final prefs = await SharedPreferences.getInstance();
    String? languageCode = prefs.getString('languageCode');

    // إذا لم تكن هناك لغة محفوظة، استخدم لغة الجهاز
    if (languageCode == null) {
      // الحصول على لغة الجهاز
      final deviceLocale = WidgetsBinding.instance.platformDispatcher.locale;
      languageCode = deviceLocale.languageCode;

      // التأكد من أن اللغة مدعومة
      if (languageCode == 'ar' || languageCode == 'en') {
        return Locale(languageCode);
      }
    }

    // إذا كانت هناك لغة محفوظة، استخدمها
    return languageCode != null && (languageCode == 'ar' || languageCode == 'en')
        ? Locale(languageCode)
        : const Locale('en'); // الافتراضي إنجليزي
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'StoryAd',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
        fontFamily: 'Poppins', // الخط الافتراضي للتطبيق (غيرته إلى Poppins)
      ),
      locale: _locale,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en'), // الإنجليزية
        Locale('ar'), // العربية
      ],
      initialRoute: '/',
      routes: {
        '/': (context) => const SplashScreen(),
        '/sign_in': (context) => const SignInScreen(),
        '/main': (context) => const MainNavigationScreen(),
      },
    );
  }
}

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> with TickerProviderStateMixin {
  late AnimationController _iconController;
  late AnimationController _textController;
  late AnimationController _termsController;
  late Animation<double> _iconScaleAnimation;
  late Animation<Offset> _iconSlideAnimation;
  late Animation<double> _textScaleAnimation;
  late Animation<Offset> _textSlideAnimation;
  late Animation<double> _termsAnimation;

  @override
  void initState() {
    super.initState();

    _iconController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );
    _iconScaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _iconController, curve: Curves.easeInOut),
    );
    _iconSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0),
      end: const Offset(0, 0.2),
    ).animate(
      CurvedAnimation(parent: _iconController, curve: Curves.easeInOut),
    );

    _textController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );
    _textScaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _textController, curve: Curves.easeInOut),
    );
    _textSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0),
      end: const Offset(0, 0.2),
    ).animate(
      CurvedAnimation(parent: _textController, curve: Curves.easeInOut),
    );

    _termsController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _termsAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _termsController, curve: Curves.easeIn),
    );

    // Start all animations at the same time
    _iconController.forward();
    _textController.forward();
    _termsController.forward();

    _checkLoginStatus();
  }

  Future<void> _checkLoginStatus() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    bool isLoggedIn = prefs.getBool('isLoggedIn') ?? false;

    await Future.delayed(const Duration(seconds: 5));

    if (isLoggedIn) {
      Navigator.pushReplacementNamed(context, '/main');
    } else {
      Navigator.pushReplacementNamed(context, '/sign_in');
    }
  }

  @override
  void dispose() {
    _iconController.dispose();
    _textController.dispose();
    _termsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Scaffold(
      backgroundColor: Colors.blue[300],
      body: Container(
        width: double.infinity,
        height: double.infinity,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Spacer(flex: 1),
            Padding(
              padding: const EdgeInsets.only(top: 20.0),
              child: ScaleTransition(
                scale: _iconScaleAnimation,
                child: SlideTransition(
                  position: _iconSlideAnimation,
                  child: const Icon(
                    Icons.camera_alt,
                    color: Colors.white,
                    size: 100.0,
                  ),
                ),
              ),
            ),
            const Spacer(flex: 1),
            ScaleTransition(
              scale: _textScaleAnimation,
              child: SlideTransition(
                position: _textSlideAnimation,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      localizations.storyAd,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 50,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Poppins', // تغيير الخط إلى Poppins
                      ),
                    ),
                    const SizedBox(height: 20),
                    Text(
                      localizations.discoverAds,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 20,
                        fontFamily: 'Poppins', // تغيير الخط إلى Poppins
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const Spacer(flex: 2),
            Padding(
              padding: const EdgeInsets.only(bottom: 20.0),
              child: FadeTransition(
                opacity: _termsAnimation,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 30),
                  child: Text(
                    localizations.termsAndConditions,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                      fontFamily: 'Poppins', // تغيير الخط إلى Poppins
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}