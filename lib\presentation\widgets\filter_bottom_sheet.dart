import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../core/models/ad_model.dart';
import '../../features/ads/bloc/ads_bloc.dart';

class FilterBottomSheet extends StatefulWidget {
  const FilterBottomSheet({super.key});

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  List<AdCategory> selectedCategories = [];

  final Map<AdCategory, String> categoryNames = {
    AdCategory.cars: 'Cars',
    AdCategory.fashion: 'Fashion',
    AdCategory.technology: 'Technology',
    AdCategory.food: 'Food',
    AdCategory.beauty: 'Beauty',
    AdCategory.sports: 'Sports',
    AdCategory.travel: 'Travel',
    AdCategory.home: 'Home',
    AdCategory.health: 'Health',
    AdCategory.education: 'Education',
    AdCategory.entertainment: 'Entertainment',
    AdCategory.other: 'Other',
  };

  final Map<AdCategory, IconData> categoryIcons = {
    AdCategory.cars: Icons.directions_car,
    AdCategory.fashion: Icons.checkroom,
    AdCategory.technology: Icons.smartphone,
    AdCategory.food: Icons.restaurant,
    AdCategory.beauty: Icons.face,
    AdCategory.sports: Icons.sports_soccer,
    AdCategory.travel: Icons.flight,
    AdCategory.home: Icons.home,
    AdCategory.health: Icons.health_and_safety,
    AdCategory.education: Icons.school,
    AdCategory.entertainment: Icons.movie,
    AdCategory.other: Icons.category,
  };

  @override
  void initState() {
    super.initState();
    final state = context.read<AdsBloc>().state;
    if (state is AdsLoaded) {
      selectedCategories = List.from(state.selectedCategories);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                Text(
                  'Filter Ads',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    setState(() {
                      selectedCategories.clear();
                    });
                  },
                  child: const Text('Clear All'),
                ),
              ],
            ),
          ),
          
          const Divider(height: 1),
          
          // Categories
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Categories',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  Expanded(
                    child: GridView.builder(
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 3,
                        crossAxisSpacing: 12,
                        mainAxisSpacing: 12,
                      ),
                      itemCount: AdCategory.values.length,
                      itemBuilder: (context, index) {
                        final category = AdCategory.values[index];
                        final isSelected = selectedCategories.contains(category);
                        
                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              if (isSelected) {
                                selectedCategories.remove(category);
                              } else {
                                selectedCategories.add(category);
                              }
                            });
                          },
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 200),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? theme.primaryColor
                                  : Colors.grey[100],
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: isSelected
                                    ? theme.primaryColor
                                    : Colors.grey[300]!,
                                width: 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  categoryIcons[category],
                                  color: isSelected
                                      ? Colors.white
                                      : Colors.grey[600],
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    categoryNames[category]!,
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      color: isSelected
                                          ? Colors.white
                                          : Colors.grey[700],
                                      fontWeight: isSelected
                                          ? FontWeight.w600
                                          : FontWeight.normal,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ).animate(target: isSelected ? 1 : 0).scale(
                                begin: const Offset(1, 1),
                                end: const Offset(1.02, 1.02),
                                duration: const Duration(milliseconds: 200),
                              ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Apply Button
          Container(
            padding: const EdgeInsets.all(20),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  context.read<AdsBloc>().add(
                        FilterAdsByCategory(selectedCategories),
                      );
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(
                  selectedCategories.isEmpty
                      ? 'Show All Ads'
                      : 'Apply Filters (${selectedCategories.length})',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    ).animate().slideY(
          begin: 1,
          end: 0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOutCubic,
        );
  }
}
