import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';

part 'payment_model.g.dart';

@HiveType(typeId: 8)
class PaymentModel extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final String adId;
  
  @HiveField(3)
  final double amount;
  
  @HiveField(4)
  final String currency;
  
  @HiveField(5)
  final PaymentMethod paymentMethod;
  
  @HiveField(6)
  final PaymentStatus status;
  
  @HiveField(7)
  final DateTime createdAt;
  
  @HiveField(8)
  final DateTime? completedAt;
  
  @HiveField(9)
  final String? transactionId;
  
  @HiveField(10)
  final String? failureReason;
  
  @HiveField(11)
  final Map<String, dynamic>? metadata;

  const PaymentModel({
    required this.id,
    required this.userId,
    required this.adId,
    required this.amount,
    required this.currency,
    required this.paymentMethod,
    required this.status,
    required this.createdAt,
    this.completedAt,
    this.transactionId,
    this.failureReason,
    this.metadata,
  });

  @override
  List<Object?> get props => [
        id,
        userId,
        adId,
        amount,
        currency,
        paymentMethod,
        status,
        createdAt,
        completedAt,
        transactionId,
        failureReason,
        metadata,
      ];

  PaymentModel copyWith({
    String? id,
    String? userId,
    String? adId,
    double? amount,
    String? currency,
    PaymentMethod? paymentMethod,
    PaymentStatus? status,
    DateTime? createdAt,
    DateTime? completedAt,
    String? transactionId,
    String? failureReason,
    Map<String, dynamic>? metadata,
  }) {
    return PaymentModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      adId: adId ?? this.adId,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
      transactionId: transactionId ?? this.transactionId,
      failureReason: failureReason ?? this.failureReason,
      metadata: metadata ?? this.metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'adId': adId,
      'amount': amount,
      'currency': currency,
      'paymentMethod': paymentMethod.name,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'transactionId': transactionId,
      'failureReason': failureReason,
      'metadata': metadata,
    };
  }

  factory PaymentModel.fromJson(Map<String, dynamic> json) {
    return PaymentModel(
      id: json['id'],
      userId: json['userId'],
      adId: json['adId'],
      amount: json['amount'].toDouble(),
      currency: json['currency'],
      paymentMethod: PaymentMethod.values.firstWhere(
        (method) => method.name == json['paymentMethod'],
        orElse: () => PaymentMethod.creditCard,
      ),
      status: PaymentStatus.values.firstWhere(
        (status) => status.name == json['status'],
        orElse: () => PaymentStatus.pending,
      ),
      createdAt: DateTime.parse(json['createdAt']),
      completedAt: json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null,
      transactionId: json['transactionId'],
      failureReason: json['failureReason'],
      metadata: json['metadata'] != null ? Map<String, dynamic>.from(json['metadata']) : null,
    );
  }
}

@HiveType(typeId: 9)
enum PaymentMethod {
  @HiveField(0)
  creditCard,
  
  @HiveField(1)
  debitCard,
  
  @HiveField(2)
  paypal,
  
  @HiveField(3)
  applePay,
  
  @HiveField(4)
  googlePay,
  
  @HiveField(5)
  bankTransfer,
  
  @HiveField(6)
  cryptocurrency,
}

@HiveType(typeId: 10)
enum PaymentStatus {
  @HiveField(0)
  pending,
  
  @HiveField(1)
  processing,
  
  @HiveField(2)
  completed,
  
  @HiveField(3)
  failed,
  
  @HiveField(4)
  cancelled,
  
  @HiveField(5)
  refunded,
}

@HiveType(typeId: 11)
class PaymentCard extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final String last4Digits;
  
  @HiveField(3)
  final String brand;
  
  @HiveField(4)
  final int expiryMonth;
  
  @HiveField(5)
  final int expiryYear;
  
  @HiveField(6)
  final String holderName;
  
  @HiveField(7)
  final bool isDefault;
  
  @HiveField(8)
  final DateTime createdAt;

  const PaymentCard({
    required this.id,
    required this.userId,
    required this.last4Digits,
    required this.brand,
    required this.expiryMonth,
    required this.expiryYear,
    required this.holderName,
    this.isDefault = false,
    required this.createdAt,
  });

  @override
  List<Object> get props => [
        id,
        userId,
        last4Digits,
        brand,
        expiryMonth,
        expiryYear,
        holderName,
        isDefault,
        createdAt,
      ];

  PaymentCard copyWith({
    String? id,
    String? userId,
    String? last4Digits,
    String? brand,
    int? expiryMonth,
    int? expiryYear,
    String? holderName,
    bool? isDefault,
    DateTime? createdAt,
  }) {
    return PaymentCard(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      last4Digits: last4Digits ?? this.last4Digits,
      brand: brand ?? this.brand,
      expiryMonth: expiryMonth ?? this.expiryMonth,
      expiryYear: expiryYear ?? this.expiryYear,
      holderName: holderName ?? this.holderName,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'last4Digits': last4Digits,
      'brand': brand,
      'expiryMonth': expiryMonth,
      'expiryYear': expiryYear,
      'holderName': holderName,
      'isDefault': isDefault,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory PaymentCard.fromJson(Map<String, dynamic> json) {
    return PaymentCard(
      id: json['id'],
      userId: json['userId'],
      last4Digits: json['last4Digits'],
      brand: json['brand'],
      expiryMonth: json['expiryMonth'],
      expiryYear: json['expiryYear'],
      holderName: json['holderName'],
      isDefault: json['isDefault'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  String get maskedNumber => '**** **** **** $last4Digits';
  
  String get expiryDate => '${expiryMonth.toString().padLeft(2, '0')}/${expiryYear.toString().substring(2)}';
  
  bool get isExpired {
    final now = DateTime.now();
    final expiry = DateTime(expiryYear, expiryMonth + 1, 0);
    return now.isAfter(expiry);
  }
}
