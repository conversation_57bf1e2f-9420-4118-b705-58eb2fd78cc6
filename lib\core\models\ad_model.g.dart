// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ad_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AdModelAdapter extends TypeAdapter<AdModel> {
  @override
  final int typeId = 2;

  @override
  AdModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AdModel(
      id: fields[0] as String,
      title: fields[1] as String,
      description: fields[2] as String,
      mediaUrls: (fields[3] as List).cast<String>(),
      category: fields[4] as AdCategory,
      advertiser: fields[5] as UserModel,
      isVideo: fields[6] as bool,
      price: fields[7] as double?,
      currency: fields[8] as String?,
      productUrl: fields[9] as String?,
      tags: (fields[10] as List).cast<String>(),
      stats: fields[11] as AdStats,
      createdAt: fields[12] as DateTime,
      expiresAt: fields[13] as DateTime?,
      status: fields[14] as AdStatus,
      location: fields[15] as String?,
      priority: fields[16] as AdPriority,
    );
  }

  @override
  void write(BinaryWriter writer, AdModel obj) {
    writer
      ..writeByte(17)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.mediaUrls)
      ..writeByte(4)
      ..write(obj.category)
      ..writeByte(5)
      ..write(obj.advertiser)
      ..writeByte(6)
      ..write(obj.isVideo)
      ..writeByte(7)
      ..write(obj.price)
      ..writeByte(8)
      ..write(obj.currency)
      ..writeByte(9)
      ..write(obj.productUrl)
      ..writeByte(10)
      ..write(obj.tags)
      ..writeByte(11)
      ..write(obj.stats)
      ..writeByte(12)
      ..write(obj.createdAt)
      ..writeByte(13)
      ..write(obj.expiresAt)
      ..writeByte(14)
      ..write(obj.status)
      ..writeByte(15)
      ..write(obj.location)
      ..writeByte(16)
      ..write(obj.priority);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AdModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AdStatsAdapter extends TypeAdapter<AdStats> {
  @override
  final int typeId = 3;

  @override
  AdStats read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AdStats(
      views: fields[0] as int,
      likes: fields[1] as int,
      comments: fields[2] as int,
      shares: fields[3] as int,
      clicks: fields[4] as int,
      engagementRate: fields[5] as double,
    );
  }

  @override
  void write(BinaryWriter writer, AdStats obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.views)
      ..writeByte(1)
      ..write(obj.likes)
      ..writeByte(2)
      ..write(obj.comments)
      ..writeByte(3)
      ..write(obj.shares)
      ..writeByte(4)
      ..write(obj.clicks)
      ..writeByte(5)
      ..write(obj.engagementRate);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AdStatsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AdCategoryAdapter extends TypeAdapter<AdCategory> {
  @override
  final int typeId = 4;

  @override
  AdCategory read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return AdCategory.cars;
      case 1:
        return AdCategory.fashion;
      case 2:
        return AdCategory.technology;
      case 3:
        return AdCategory.food;
      case 4:
        return AdCategory.beauty;
      case 5:
        return AdCategory.sports;
      case 6:
        return AdCategory.travel;
      case 7:
        return AdCategory.home;
      case 8:
        return AdCategory.health;
      case 9:
        return AdCategory.education;
      case 10:
        return AdCategory.entertainment;
      case 11:
        return AdCategory.other;
      default:
        return AdCategory.cars;
    }
  }

  @override
  void write(BinaryWriter writer, AdCategory obj) {
    switch (obj) {
      case AdCategory.cars:
        writer.writeByte(0);
        break;
      case AdCategory.fashion:
        writer.writeByte(1);
        break;
      case AdCategory.technology:
        writer.writeByte(2);
        break;
      case AdCategory.food:
        writer.writeByte(3);
        break;
      case AdCategory.beauty:
        writer.writeByte(4);
        break;
      case AdCategory.sports:
        writer.writeByte(5);
        break;
      case AdCategory.travel:
        writer.writeByte(6);
        break;
      case AdCategory.home:
        writer.writeByte(7);
        break;
      case AdCategory.health:
        writer.writeByte(8);
        break;
      case AdCategory.education:
        writer.writeByte(9);
        break;
      case AdCategory.entertainment:
        writer.writeByte(10);
        break;
      case AdCategory.other:
        writer.writeByte(11);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AdCategoryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AdStatusAdapter extends TypeAdapter<AdStatus> {
  @override
  final int typeId = 5;

  @override
  AdStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return AdStatus.active;
      case 1:
        return AdStatus.paused;
      case 2:
        return AdStatus.expired;
      case 3:
        return AdStatus.rejected;
      default:
        return AdStatus.active;
    }
  }

  @override
  void write(BinaryWriter writer, AdStatus obj) {
    switch (obj) {
      case AdStatus.active:
        writer.writeByte(0);
        break;
      case AdStatus.paused:
        writer.writeByte(1);
        break;
      case AdStatus.expired:
        writer.writeByte(2);
        break;
      case AdStatus.rejected:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AdStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AdPriorityAdapter extends TypeAdapter<AdPriority> {
  @override
  final int typeId = 6;

  @override
  AdPriority read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return AdPriority.low;
      case 1:
        return AdPriority.normal;
      case 2:
        return AdPriority.high;
      case 3:
        return AdPriority.premium;
      default:
        return AdPriority.low;
    }
  }

  @override
  void write(BinaryWriter writer, AdPriority obj) {
    switch (obj) {
      case AdPriority.low:
        writer.writeByte(0);
        break;
      case AdPriority.normal:
        writer.writeByte(1);
        break;
      case AdPriority.high:
        writer.writeByte(2);
        break;
      case AdPriority.premium:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AdPriorityAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
