// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'analytics_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AdAnalyticsAdapter extends TypeAdapter<AdAnalytics> {
  @override
  final int typeId = 19;

  @override
  AdAnalytics read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AdAnalytics(
      adId: fields[0] as String,
      totalViews: fields[1] as int,
      uniqueViews: fields[2] as int,
      likes: fields[3] as int,
      comments: fields[4] as int,
      shares: fields[5] as int,
      clicks: fields[6] as int,
      saves: fields[7] as int,
      engagementRate: fields[8] as double,
      clickThroughRate: fields[9] as double,
      viewsByCountry: (fields[10] as Map).cast<String, int>(),
      viewsByAge: (fields[11] as Map).cast<String, int>(),
      viewsByGender: (fields[12] as Map).cast<String, int>(),
      viewsByDevice: (fields[13] as Map).cast<String, int>(),
      dailyStats: (fields[14] as List).cast<DailyStats>(),
      lastUpdated: fields[15] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, AdAnalytics obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.adId)
      ..writeByte(1)
      ..write(obj.totalViews)
      ..writeByte(2)
      ..write(obj.uniqueViews)
      ..writeByte(3)
      ..write(obj.likes)
      ..writeByte(4)
      ..write(obj.comments)
      ..writeByte(5)
      ..write(obj.shares)
      ..writeByte(6)
      ..write(obj.clicks)
      ..writeByte(7)
      ..write(obj.saves)
      ..writeByte(8)
      ..write(obj.engagementRate)
      ..writeByte(9)
      ..write(obj.clickThroughRate)
      ..writeByte(10)
      ..write(obj.viewsByCountry)
      ..writeByte(11)
      ..write(obj.viewsByAge)
      ..writeByte(12)
      ..write(obj.viewsByGender)
      ..writeByte(13)
      ..write(obj.viewsByDevice)
      ..writeByte(14)
      ..write(obj.dailyStats)
      ..writeByte(15)
      ..write(obj.lastUpdated);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AdAnalyticsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class DailyStatsAdapter extends TypeAdapter<DailyStats> {
  @override
  final int typeId = 20;

  @override
  DailyStats read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return DailyStats(
      date: fields[0] as DateTime,
      views: fields[1] as int,
      likes: fields[2] as int,
      comments: fields[3] as int,
      shares: fields[4] as int,
      clicks: fields[5] as int,
    );
  }

  @override
  void write(BinaryWriter writer, DailyStats obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.date)
      ..writeByte(1)
      ..write(obj.views)
      ..writeByte(2)
      ..write(obj.likes)
      ..writeByte(3)
      ..write(obj.comments)
      ..writeByte(4)
      ..write(obj.shares)
      ..writeByte(5)
      ..write(obj.clicks);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DailyStatsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class UserAnalyticsAdapter extends TypeAdapter<UserAnalytics> {
  @override
  final int typeId = 21;

  @override
  UserAnalytics read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserAnalytics(
      userId: fields[0] as String,
      totalAds: fields[1] as int,
      activeAds: fields[2] as int,
      totalViews: fields[3] as int,
      totalLikes: fields[4] as int,
      totalComments: fields[5] as int,
      totalShares: fields[6] as int,
      totalFollowers: fields[7] as int,
      totalFollowing: fields[8] as int,
      averageEngagementRate: fields[9] as double,
      topPerformingAdId: fields[10] as String,
      monthlyStats: (fields[11] as List).cast<MonthlyStats>(),
      lastUpdated: fields[12] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, UserAnalytics obj) {
    writer
      ..writeByte(13)
      ..writeByte(0)
      ..write(obj.userId)
      ..writeByte(1)
      ..write(obj.totalAds)
      ..writeByte(2)
      ..write(obj.activeAds)
      ..writeByte(3)
      ..write(obj.totalViews)
      ..writeByte(4)
      ..write(obj.totalLikes)
      ..writeByte(5)
      ..write(obj.totalComments)
      ..writeByte(6)
      ..write(obj.totalShares)
      ..writeByte(7)
      ..write(obj.totalFollowers)
      ..writeByte(8)
      ..write(obj.totalFollowing)
      ..writeByte(9)
      ..write(obj.averageEngagementRate)
      ..writeByte(10)
      ..write(obj.topPerformingAdId)
      ..writeByte(11)
      ..write(obj.monthlyStats)
      ..writeByte(12)
      ..write(obj.lastUpdated);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserAnalyticsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class MonthlyStatsAdapter extends TypeAdapter<MonthlyStats> {
  @override
  final int typeId = 22;

  @override
  MonthlyStats read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MonthlyStats(
      month: fields[0] as DateTime,
      views: fields[1] as int,
      likes: fields[2] as int,
      comments: fields[3] as int,
      shares: fields[4] as int,
      followers: fields[5] as int,
      newAds: fields[6] as int,
    );
  }

  @override
  void write(BinaryWriter writer, MonthlyStats obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.month)
      ..writeByte(1)
      ..write(obj.views)
      ..writeByte(2)
      ..write(obj.likes)
      ..writeByte(3)
      ..write(obj.comments)
      ..writeByte(4)
      ..write(obj.shares)
      ..writeByte(5)
      ..write(obj.followers)
      ..writeByte(6)
      ..write(obj.newAds);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MonthlyStatsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
